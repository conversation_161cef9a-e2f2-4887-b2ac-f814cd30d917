services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile.fastapi
    image: $ECR_REGISTRY/$ECR_REPOSITORY:backend-latest
    ports:
      - "8000:8000"
    command: uvicorn app:app --host 0.0.0.0 --port 8000 --reload
 
  agent:
    build:
      context: .
      dockerfile: Dockerfile.agent
    image: $ECR_REGISTRY/$ECR_REPOSITORY:agent-latest
    environment:
        - LIVEKIT_URL=${LIVEKIT_URL}
        - LIVEKIT_API_KEY=${LIVEKIT_API_KEY}
        - LIVEKIT_API_SECRET=${LIVEKIT_API_SECRET}
        - PINECONE_API_KEY=${PINECONE_API_KEY}
        - DEEPGRAM_API_KEY=${DEEPGRAM_API_KEY}
        - GROQ_API_KEY=${GROQ_API_KEY}
        - TAVLY_API_KEY=${TAVLY_API_KEY}
        - CARTESIA_API_KEY=${CARTESIA_API_KEY}
        - SPEECHIFY_API_KEY=${SPEECHIFY_API_KEY}
    command: python langgraph_agent.py start