import sys
from typing import  Dict, Any

from langchain_core.output_parsers import StrOutputParser
from langchain.prompts import ChatPromptTemplate

from src.agent.constant import GraphState
from src.exception import CustomException
from src.logging.logger import logging
from src.prompt import GENERATION_PROMPT
from src.utils.main_utils import preprocess_web_doc
from src.utils.main_utils import measure_time

class GenerationTool:
    def __init__(self,llm) -> None:
        self.llm = llm
       
    @measure_time
    def generate(self, state: GraphState) -> Dict[str, Any]:

        logging.info(f"=========================== GenerationTool Invoke ===========================")
        print("=" * 20, 'Generation Tool Invoked', "=" * 20)
        question = state['question']
        documents = state['documents']
    
        logging.info(f"Input: {documents}")

        try:
            # Limit context size for faster processing
           
            max_context = 1500  # Reduced context size
            logging.info("Reducing the context length")             
            
            logging.info(f"Genearation Context: {documents}")
          
            
            # Use pre-compiled prompt for speed
            prompt_template = ChatPromptTemplate.from_template(GENERATION_PROMPT)
            
            generation_chain = prompt_template | self.llm | StrOutputParser()

            output = generation_chain.invoke({"context": documents, "question": question})

            logging.info(f"Generation Output :\n\t {output}")
            logging.info(f"=========================== GenerationTool Invoke Completed ===========================")
            return {"generation": output, "documents": documents, "question": question}
        
        except Exception as e:
            logging.error(f"Exception in Generate :{e}")
            raise CustomException(e,sys)

  
