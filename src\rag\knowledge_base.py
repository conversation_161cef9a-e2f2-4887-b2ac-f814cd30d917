import os
import sys
import getpass
from pathlib import Path
from typing import Dict, Optional, List, Any

from langchain.schema import Document
from langchain_community.document_loaders import PyPDFLoader, TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import Pinecone
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_core.embeddings import Embeddings
from langchain_core.documents import Document

from pinecone import Pinecone, ServerlessSpec
from langchain_pinecone import PineconeVectorStore
from dotenv import load_dotenv
load_dotenv()


from src.logging.logger import logging
from src.exception import CustomException

GROQ_API_KEY = os.getenv("GROQ_API_KEY")
PINE_CONE_API_KEY = os.getenv("PINE_CONE_API_KEY")

index_name = os.getenv("INDEX")



class RAGSystem:
    """
    Handles document loading, indexing, and retrieval functionality.
    Responsible for creating vector stores and managing document-specific operations.
    """
    def __init__(self,embeddings_model:str ,file_path: str = "data/raw"):
        self.embeddings_model = embeddings_model
        self.file_path = file_path
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size = 1024,
            chunk_overlap = 100,
            length_function=len,
            separators=["\n\n", "\n", ". ", " ", ""]
        )

        logging.info("======================= Knowledge Base ========================")
        
    def ensure_data_directory(self):
        """Ensures the data directory exists for storing documents."""
        data_dir = Path(self.file_path)
        data_dir.mkdir(exist_ok=True)
        (f"Checked/Created data directory: '{self.file_path}'")

    def load_document(self) -> Optional[List[Document]]:
        """
        Loads documents from a directory and splits them into chunks.

        Args:
            file_path: Path to a directory containing document files

        Returns:
            List of document chunks or None if loading fails
        """
        try:
            dir_path = Path(self.file_path)
            all_documents = []

            for file in dir_path.iterdir():
                if file.suffix == '.pdf':
                    loader = PyPDFLoader(str(file))
            
                else:
                    print(f"Skipping unsupported file type: {file.name}")
                    continue

                # Load the document
                docs = loader.load()

                # Add metadata and wrap in Document schema
                for doc in docs:
                    src = doc.metadata.get("source", str(file.name))
                    all_documents.append(
                        Document(
                            page_content=doc.page_content,
                            metadata={'source': src}
                        )
                    )

            if not all_documents:
                print(f"No valid documents found in '{self.file_path}'")
                return None

            # Split into chunks
            self.doc_chunks = self.text_splitter.split_documents(all_documents)

            if not self.doc_chunks:
                print(f"No chunks generated from '{self.file_path}'.")
                return None

            logging.info(f"Loaded documents from '{self.file_path}': {len(self.doc_chunks)} chunks.")
            print(f"Loaded documents from '{self.file_path}': {len(self.doc_chunks)} chunks.")
            return self.doc_chunks

        except Exception as e:
            logging.error(f"Failed to load documents from '{self.file_path}': {e}")
            raise CustomException(e,sys)

       
    def _load_embedings(self):
        """
        Download and return the HuggingFace embeddings model.
        """
        model_kwargs = {'device': 'cpu'}
        encode_kwargs = {'normalize_embeddings': False}
        embeddings = HuggingFaceEmbeddings(
            model_name=self.embeddings_model,
            model_kwargs=model_kwargs,
            encode_kwargs=encode_kwargs
        )
        # Embedding dimension
        dim = len(embeddings.embed_query("hello"))
        return embeddings, dim


    def _vectore_store_dense(self,embeddings: Embeddings, embeddings_dim: int, doc_chunks: List):
        try:
            if not os.getenv("PINECONE_API_KEY"):
                os.environ["PINECONE_API_KEY"] = getpass.getpass("Enter your Pinecone API key: ")
                
            pinecone_api_key = os.environ.get("PINECONE_API_KEY")
            index_name = "agentic-rag"
            pc = Pinecone(api_key=pinecone_api_key)

            if not pc.has_index(index_name):
                pc.create_index(
                    name=index_name,
                    dimension=embeddings_dim,
                    metric="cosine",
                    spec=ServerlessSpec(cloud="aws", region="us-east-1"),
                )

            index = pc.Index(index_name)

            vector_store = PineconeVectorStore(index=index, embedding=embeddings)
            vector_store.from_documents(
                documents=doc_chunks,
                index_name=index_name,
                embedding=embeddings
            )

    
            print("PinecodeDB vector store setup Completed.")
            logging.info("PinecodeDB vector store setup Completed.")
            return vector_store

        except Exception as e:
            print(f"Error in vector DB: {e}")
            raise e
    

    def run_piepline(self):
        try:
            self.ensure_data_directory()
            print("Data Dir present")
            print()
            
            docs_chunks = self.load_document()
            print("Chunking Completed")
            print(docs_chunks[1])
            embedings, dim = self._load_embedings()
            print(f"Dimenson of Model Embeddings: {dim}")
            logging.info("Embeddings model loaded")
            print()
            vector_store=self._vectore_store_dense(embeddings=embedings,embeddings_dim=dim,doc_chunks=docs_chunks)
            print("Data Store to Vectore Db Completed")
            logging.info("======================= Knowledge Base ========================")
            
            return vector_store


        except Exception as e:
            logging.error(f"Eror in RAG pipeline: {e}")
            raise CustomException(e,sys)
             
        

if __name__=="__main__":
    obj = RAGSystem(
        embeddings_model="sentence-transformers/all-MiniLM-L6-v2",
        file_path=r"D:\GENAIProjects\calling-repasentive-ai\calling-agent\data"
    )
    obj.run_piepline()