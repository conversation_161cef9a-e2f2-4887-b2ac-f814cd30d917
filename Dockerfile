FROM python:3.11-slim
 
# Install system dependencies and supervisor
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    supervisor \
&& rm -rf /var/lib/apt/lists/*
 
# Set the working directory
WORKDIR /app
 
# Copy application code
COPY . .
 
# Install Python dependencies
RUN pip install -r requirements.txt
 
# Download files for the agent
RUN python langgraph_agent.py download-files
 
# Create supervisor configuration
RUN echo '[supervisord]\n\
nodaemon=true\n\
user=root\n\
\n\
[program:fastapi]\n\
command=uvicorn app:app --host 0.0.0.0 --port 8000 --reload\n\
directory=/app\n\
autostart=true\n\
autorestart=true\n\
stdout_logfile=/var/log/fastapi.log\n\
stderr_logfile=/var/log/fastapi_error.log\n\
\n\
[program:agent]\n\
command=python langgraph_agent.py start\n\
directory=/app\n\
autostart=true\n\
autorestart=true\n\
stdout_logfile=/var/log/agent.log\n\
stderr_logfile=/var/log/agent_error.log' > /etc/supervisor/conf.d/supervisord.conf
 
# Create log directory
RUN mkdir -p /var/log
 
# Expose port for FastAPI
EXPOSE 8000
 
# Start supervisor to manage both processes
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]