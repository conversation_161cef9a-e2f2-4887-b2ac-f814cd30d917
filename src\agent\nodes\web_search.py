import os
import sys
import requests
from dotenv import load_dotenv
from typing import List, Dict, Any
from langchain.schema import Document

from src.agent.constant import GraphState
from src.exception import CustomException
from src.logging.logger import logging
from src.utils.main_utils import measure_time

load_dotenv()

class TaivilySearchTool:
    def __init__(self) -> None:
        self.api_key = os.getenv('TAVLY_API_KEY')
        self.base_url = "https://api.tavily.com/search"
    
        

   
    def search(self,query: str, max_results: int = 3) -> List[Dict]:
        try:
            payload = {
                "api_key": self.api_key,
                "query": query,
                "search_depth": "advanced",
                "include_answer": True,
                "include_images": False,
                "include_raw_content": False,
                "max_results": max_results
            }
            response = requests.post(self.base_url, json=payload, timeout=15)
            response.raise_for_status()
            data = response.json()

            logging.info(data)

            content_list = data["results"]
            print(content_list)
            logging.info("Web Search Completed")
            final_answer = "\n".join([i['content'] for i in content_list])
            logging.info(f"Final Answer: {final_answer}")
            return final_answer
            
        except Exception as e:
            logging.error(f"Exception raise: {e}")
            raise CustomException(e,sys)
    @measure_time
    def web_search(self, state: GraphState) -> Dict[str, Any]:
        """Web search tool to fetch information from the web."""
        
        logging.info(f"=========================== TaivilySearchTool Invoke ===========================")
        print("="*20, 'Web Search Tool Invoked', "="*20)
        question = state['question']
        documents = state.get('documents', [])
        try:
            logging.info("Web Search Started")
          

            # getting web search results
            web_search_results = self.search(question)
           
            documents = Document(page_content=web_search_results)
            
            print("="*20, 'Web Search Tool Completed', "="*20)
            logging.info("web Search Successfully Completed !")

            logging.info(f"=========================== TaivilySearchTool Invoke Completed ===========================")
            return {"documents": documents, "question": question}
        
        except Exception as e :
            logging.error(f"Exception raise in web search: {e}")
            raise CustomException(e,sys)      

    
    