import os
import sys
import json
import asyncio
import subprocess
import threading
import time
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path
from fastapi.responses import FileResponse
import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from pydantic import BaseModel

# Import your existing modules - with fallback
import logging

# Configure basic logging
logging.basicConfig(level=logging.INFO)

try:
    from src.logging.call_logger import call_logger
except ImportError:
    logging.warning("call_logger not available, using mock")
    class MockCallLogger:
        def get_active_calls(self):
            return {}
    call_logger = MockCallLogger()

# Configuration models
class ModelConfig(BaseModel):
    model: str
    api_key: str

class SettingsConfig(BaseModel):
    stt: ModelConfig
    llm: ModelConfig
    tts: ModelConfig

class ServerStatus(BaseModel):
    status: str
    pid: Optional[int] = None
    uptime: Optional[float] = None

# Global variables for server management
langgraph_process = None
server_start_time = None
config_file_path = "config/model_settings.json"

app = FastAPI(
    title="AI Voice System Backend",
    description="FastAPI backend for AI Voice Conversation Interface",
    version="1.0.0"
)

# CORS middleware - Allow frontend connections
origins = [
    "https://voice-agent-front-end11.vercel.app",
    "https://voice-agent-front-end11.vercel.app",
    "https://voice-agent-front-end11-git-main-llaisolutions-projects.vercel.app",
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://localhost:5173",
    "http://127.0.0.1:5173",
    "http://localhost:5174",
    "http://127.0.0.1:5174",
    "http://localhost:8080",
    "http://127.0.0.1:8080",
]


app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Ensure config directory exists
os.makedirs("config", exist_ok=True)

# Add request logging middleware
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    print(f"Incoming request: {request.method} {request.url}")
    print(f"Headers: {dict(request.headers)}")
    
    response = await call_next(request)
    
    process_time = time.time() - start_time
    print(f"Response: {response.status_code} - Process time: {process_time:.2f}s")
    
    return response

# Global OPTIONS handler for CORS preflight
@app.options("/{path:path}")
async def options_handler(path: str):
    """Handle OPTIONS requests for CORS preflight"""
    return JSONResponse(
        status_code=200,
        content={"message": "OK"},
        headers={
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
            "Access-Control-Allow-Headers": "*",
        }
    )

def load_settings() -> Optional[Dict[str, Any]]:
    """Load saved model settings from file"""
    try:
        if os.path.exists(config_file_path):
            with open(config_file_path, 'r') as f:
                return json.load(f)
    except Exception as e:
        logging.error(f"Error loading settings: {e}")
    return None

def save_settings(settings: Dict[str, Any]) -> bool:
    """Save model settings to file"""
    try:
        with open(config_file_path, 'w') as f:
            json.dump(settings, f, indent=2)
        return True
    except Exception as e:
        logging.error(f"Error saving settings: {e}")
        return False

# This catch-all route is moved to the end of the file to avoid interfering with API routes

@app.get("/api/health")
@app.options("/api/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "server_status": get_server_status()
    }

def get_server_status() -> Dict[str, Any]:
    """Get current langgraph server status"""
    global langgraph_process, server_start_time

    if langgraph_process is None:
        return {"status": "stopped", "pid": None, "uptime": None}

    # Check if process is still running
    if langgraph_process.poll() is not None:
        # Process has terminated
        langgraph_process = None
        server_start_time = None
        return {"status": "stopped", "pid": None, "uptime": None}

    uptime = time.time() - server_start_time if server_start_time else 0
    return {
        "status": "running",
        "pid": langgraph_process.pid,
        "uptime": uptime
    }

# Model Configuration Endpoints
@app.post("/api/save-settings")
async def save_model_settings(settings: SettingsConfig):
    """Save model configuration settings"""
    try:
        settings_dict = {
            "stt": {"model": settings.stt.model, "api_key": settings.stt.api_key},
            "llm": {"model": settings.llm.model, "api_key": settings.llm.api_key},
            "tts": {"model": settings.tts.model, "api_key": settings.tts.api_key},
            "timestamp": datetime.now().isoformat()
        }

        if save_settings(settings_dict):
            logging.info(f"Settings saved successfully: {settings_dict}")
            return {"status": "success", "message": "Settings saved successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to save settings")

    except Exception as e:
        logging.error(f"Error saving settings: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/load-settings")
async def load_model_settings():
    """Load saved model configuration settings"""
    try:
        settings = load_settings()
        if settings:
            return {"status": "success", "settings": settings}
        else:
            return {"status": "success", "settings": None, "message": "No saved settings found"}
    except Exception as e:
        logging.error(f"Error loading settings: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/available-models")
async def get_available_models():
    """Get available models for STT, LLM, and TTS"""
    try:
        available_models = {
            "stt": {
                "models": [
                    {
                        "value": "whisper-large-v3-turbo",
                        "label": "Whisper Large V3 Turbo (Groq)",
                        "provider": "groq",
                        "endpoint": "https://api.groq.com/openai/v1/audio/translations"
                    },
                    {
                        "value": "whisper-large-v3",
                        "label": "Whisper Large V3 (Groq)",
                        "provider": "groq", 
                        "endpoint": "https://api.groq.com/openai/v1/audio/translations"
                    }
                ],
                "default": "whisper-large-v3-turbo"
            },
            "llm": {
                "models": [
                    {
                        "value": "llama3-8b-8192",
                        "label": "Llama 3 8B (Groq)",
                        "provider": "groq",
                        "endpoint": "https://api.groq.com/openai/v1/chat/completions"
                    },
                    {
                        "value": "llama-3.1-8b-instant",
                        "label": "Llama 3.1 8B Instant (Groq)",
                        "provider": "groq",
                        "endpoint": "https://api.groq.com/openai/v1/chat/completions"
                    },
                    {
                        "value": "llama/llama-4-maverick-17b-128e-instruct",
                        "label": "Llama 4 Maverick 17B (Groq)",
                        "provider": "groq",
                        "endpoint": "https://api.groq.com/openai/v1/chat/completions"
                    }
                ],
                "default": "llama3-8b-8192"
            },
            "tts": {
                "models": [
                    {
                        "value": "simba-multilingual",
                        "label": "Simba Multilingual (Speechify)",
                        "provider": "speechify",
                        "endpoint": "https://api.speechify.com/v1/audio/speech"
                    }
                ],
                "default": "simba-multilingual"
            }
        }
        
        return {"status": "success", "models": available_models}
    except Exception as e:
        logging.error(f"Error fetching available models: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Server Control Endpoints
@app.post("/api/start-server")
async def start_langgraph_server(background_tasks: BackgroundTasks):
    """Start the langgraph-agent.py server"""
    global langgraph_process, server_start_time

    try:
        # Check if server is already running
        if langgraph_process is not None and langgraph_process.poll() is None:
            return {"status": "already_running", "message": "Server is already running"}

        # Load current settings to pass to the agent
        settings = load_settings()
        if not settings:
            raise HTTPException(status_code=400, detail="No model settings found. Please save settings first.")

        # Set environment variables for the agent
        env = os.environ.copy()
        if settings.get("stt", {}).get("api_key"):
            env["GROQ_API_KEY"] = settings["stt"]["api_key"]
        if settings.get("llm", {}).get("api_key"):
            env["GROQ_API_KEY"] = settings["llm"]["api_key"]  # Assuming same key for now

        # Start the langgraph agent process
        cmd = [sys.executable, "langgraph-agent.py"]
        langgraph_process = subprocess.Popen(
            cmd,
            cwd=os.getcwd(),
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )

        server_start_time = time.time()

        logging.info(f"Started langgraph-agent.py with PID: {langgraph_process.pid}")

        return {
            "status": "started",
            "message": "Server started successfully",
            "pid": langgraph_process.pid,
            "settings_applied": settings
        }

    except Exception as e:
        logging.error(f"Error starting server: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/stop-server")
async def stop_langgraph_server():
    """Stop the langgraph-agent.py server"""
    global langgraph_process, server_start_time

    try:
        if langgraph_process is None:
            return {"status": "not_running", "message": "Server is not running"}

        # Terminate the process
        langgraph_process.terminate()

        # Wait for process to terminate (with timeout)
        try:
            langgraph_process.wait(timeout=10)
        except subprocess.TimeoutExpired:
            # Force kill if it doesn't terminate gracefully
            langgraph_process.kill()
            langgraph_process.wait()

        pid = langgraph_process.pid
        langgraph_process = None
        server_start_time = None

        logging.info(f"Stopped langgraph-agent.py (PID: {pid})")

        return {
            "status": "stopped",
            "message": "Server stopped successfully",
            "pid": pid
        }

    except Exception as e:
        logging.error(f"Error stopping server: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/server-status")
async def get_langgraph_server_status():
    """Get current server status"""
    return get_server_status()

# Real-time Data Endpoints
@app.get("/api/call-logs")
async def get_call_logs():
    """Get all call logs"""
    try:
        call_logs_dir = Path("call_logs")
        if not call_logs_dir.exists():
            return {"status": "success", "logs": [], "message": "No call logs directory found"}

        logs = []

        # Get all call log files
        for log_file in call_logs_dir.glob("call_*.txt"):
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Extract basic info from filename and content
                filename = log_file.name
                file_stats = log_file.stat()

                logs.append({
                    "filename": filename,
                    "created_time": datetime.fromtimestamp(file_stats.st_ctime).isoformat(),
                    "modified_time": datetime.fromtimestamp(file_stats.st_mtime).isoformat(),
                    "size": file_stats.st_size,
                    "preview": content[:200] + "..." if len(content) > 200 else content
                })

            except Exception as e:
                logging.error(f"Error reading log file {log_file}: {e}")
                continue

        # Sort by creation time (newest first)
        logs.sort(key=lambda x: x["created_time"], reverse=True)

        return {"status": "success", "logs": logs}

    except Exception as e:
        logging.error(f"Error fetching call logs: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/call-logs/{filename}")
async def get_call_log_detail(filename: str):
    """Get detailed content of a specific call log"""
    try:
        call_logs_dir = Path("call_logs")
        log_file = call_logs_dir / filename

        if not log_file.exists():
            raise HTTPException(status_code=404, detail="Call log file not found")

        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()

        file_stats = log_file.stat()

        return {
            "status": "success",
            "filename": filename,
            "content": content,
            "created_time": datetime.fromtimestamp(file_stats.st_ctime).isoformat(),
            "modified_time": datetime.fromtimestamp(file_stats.st_mtime).isoformat(),
            "size": file_stats.st_size
        }

    except Exception as e:
        logging.error(f"Error fetching call log detail: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/transcriptions")
async def get_recent_transcriptions():
    """Get recent transcriptions from call logs"""
    try:
        call_logs_dir = Path("call_logs")
        if not call_logs_dir.exists():
            return {"status": "success", "transcriptions": []}

        transcriptions = []

        # Get recent transcript files
        transcript_files = list(call_logs_dir.glob("call_transcript_*.txt")) + \
                          list(call_logs_dir.glob("console_transcript_*.txt"))

        # Sort by modification time (newest first)
        transcript_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

        # Get the most recent 5 files
        for transcript_file in transcript_files[:5]:
            try:
                with open(transcript_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                file_stats = transcript_file.stat()

                transcriptions.append({
                    "filename": transcript_file.name,
                    "content": content,
                    "modified_time": datetime.fromtimestamp(file_stats.st_mtime).isoformat(),
                    "type": "call" if "call_transcript" in transcript_file.name else "console"
                })

            except Exception as e:
                logging.error(f"Error reading transcript file {transcript_file}: {e}")
                continue

        return {"status": "success", "transcriptions": transcriptions}

    except Exception as e:
        logging.error(f"Error fetching transcriptions: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/live-transcription")
@app.options("/api/live-transcription")
async def get_live_transcription():
    """Get the most recent live transcription"""
    try:
        call_logs_dir = Path("call_logs")
        if not call_logs_dir.exists():
            return {"status": "success", "transcription": None}

        # Find the most recent transcript file
        transcript_files = list(call_logs_dir.glob("call_transcript_*.txt")) + \
                          list(call_logs_dir.glob("console_transcript_*.txt"))

        if not transcript_files:
            return {"status": "success", "transcription": None}

        # Get the most recently modified file
        latest_file = max(transcript_files, key=lambda x: x.stat().st_mtime)

        with open(latest_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # Extract the last few lines for live display
        lines = content.strip().split('\n')
        recent_lines = lines[-10:] if len(lines) > 10 else lines

        return {
            "status": "success",
            "transcription": {
                "filename": latest_file.name,
                "recent_content": '\n'.join(recent_lines),
                "full_content": content,
                "last_modified": datetime.fromtimestamp(latest_file.stat().st_mtime).isoformat()
            }
        }

    except Exception as e:
        logging.error(f"Error fetching live transcription: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/active-calls")
async def get_active_calls():
    """Get currently active calls"""
    try:
        active_calls = call_logger.get_active_calls()

        # Convert CallInfo objects to dictionaries for JSON serialization
        calls_data = {}
        for call_id, call_info in active_calls.items():
            calls_data[call_id] = {
                "call_id": call_info.call_id,
                "caller_phone_number": call_info.caller_phone_number,
                "trunk_phone_number": call_info.trunk_phone_number,
                "call_status": call_info.call_status,
                "start_time": call_info.start_time,
                "language_detected": call_info.language_detected,
                "transcription_count": len(call_info.transcriptions),
                "response_count": len(call_info.agent_responses)
            }

        return {"status": "success", "active_calls": calls_data}

    except Exception as e:
        logging.error(f"Error fetching active calls: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/audio-levels")
@app.options("/api/audio-levels")
async def get_audio_levels():
    """Get real-time audio levels from active calls"""
    try:
        active_calls = call_logger.get_active_calls()
        
        # Calculate audio level based on call activity
        audio_level = 0
        call_activity = False
        
        if active_calls:
            call_activity = True
            # Get the most recent call
            latest_call = max(active_calls.values(), key=lambda x: x.start_time)
            
            # Simulate audio level based on recent activity
            recent_transcriptions = latest_call.transcriptions[-5:] if latest_call.transcriptions else []
            recent_responses = latest_call.agent_responses[-5:] if latest_call.agent_responses else []
            
            # Check if there's been recent activity (within last 30 seconds)
            now = datetime.now().isoformat()
            recent_activity = False
            
            for trans in recent_transcriptions:
                if trans.get("timestamp"):
                    trans_time = datetime.fromisoformat(trans["timestamp"].replace('Z', '+00:00'))
                    if (datetime.now(trans_time.tzinfo) - trans_time).seconds < 30:
                        recent_activity = True
                        break
            
            if not recent_activity:
                for resp in recent_responses:
                    if resp.get("timestamp"):
                        resp_time = datetime.fromisoformat(resp["timestamp"].replace('Z', '+00:00'))
                        if (datetime.now(resp_time.tzinfo) - resp_time).seconds < 30:
                            recent_activity = True
                            break
            
            if recent_activity:
                # Simulate realistic audio levels during active conversation
                import random
                audio_level = random.randint(20, 85)  # Active conversation levels
            else:
                # Low level for active call but no recent speech
                audio_level = random.randint(0, 15)
        else:
            # No active calls
            audio_level = 0
        
        return {
            "status": "success",
            "audio_level": audio_level,
            "has_active_calls": call_activity,
            "active_call_count": len(active_calls),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logging.error(f"Error fetching audio levels: {e}")
        return {
            "status": "success",
            "audio_level": 0,
            "has_active_calls": False,
            "active_call_count": 0,
            "timestamp": datetime.now().isoformat()
        }

# Export endpoints
@app.get("/api/export-logs")
async def export_logs():
    """Export all logs as a downloadable file"""
    try:
        call_logs_dir = Path("call_logs")
        if not call_logs_dir.exists():
            return {"status": "success", "message": "No logs to export"}

        # Create a summary of all logs
        export_data = {
            "export_timestamp": datetime.now().isoformat(),
            "logs": []
        }

        for log_file in call_logs_dir.glob("*.txt"):
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                file_stats = log_file.stat()

                export_data["logs"].append({
                    "filename": log_file.name,
                    "content": content,
                    "created_time": datetime.fromtimestamp(file_stats.st_ctime).isoformat(),
                    "modified_time": datetime.fromtimestamp(file_stats.st_mtime).isoformat(),
                    "size": file_stats.st_size
                })

            except Exception as e:
                logging.error(f"Error reading log file {log_file}: {e}")
                continue

        return {"status": "success", "export_data": export_data}

    except Exception as e:
        logging.error(f"Error exporting logs: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Utility endpoints
@app.delete("/api/clear-logs")
async def clear_logs():
    """Clear all call logs (use with caution)"""
    try:
        call_logs_dir = Path("call_logs")
        if not call_logs_dir.exists():
            return {"status": "success", "message": "No logs to clear"}

        deleted_count = 0
        for log_file in call_logs_dir.glob("*.txt"):
            try:
                log_file.unlink()
                deleted_count += 1
            except Exception as e:
                logging.error(f"Error deleting log file {log_file}: {e}")
                continue

        return {
            "status": "success",
            "message": f"Cleared {deleted_count} log files",
            "deleted_count": deleted_count
        }

    except Exception as e:
        logging.error(f"Error clearing logs: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Catch-all route to serve React app (must be last to avoid interfering with API routes)
@app.get("/{full_path:path}")
async def serve_react_app(full_path: str):
    """Serve React app for all non-API routes"""
    
    # Don't interfere with API routes
    if full_path.startswith("api/"):
        raise HTTPException(status_code=404, detail="API endpoint not found")
    
    # Check if React build exists
    if not os.path.exists("dist/index.html"):
        raise HTTPException(
            status_code=500, 
            detail="React build files not found. Run 'npm run build' first."
        )
    
    # Serve the React index.html for all routes (client-side routing)
    return FileResponse("dist/index.html")

if __name__ == "__main__":
    print("🚀 Starting AI Voice System Backend...")
    print(f"📁 Working directory: {os.getcwd()}")
    print(f"⚙️  Config file: {config_file_path}")

    # Create logs directory if it doesn't exist
    os.makedirs("call_logs", exist_ok=True)

    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
