name: workflow
 
on:

  push:

    branches:

      - main

    paths-ignore:

      - 'README.md'
 
permissions:

  id-token: write

  contents: read
 
jobs:

  integration:

    name: Continuous Integration

    runs-on: ubuntu-latest

    steps:

      - name: Checkout Code

        uses: actions/checkout@v3
 
      - name: Lint code

        run: echo "Linting repository"
 
      - name: Run unit tests

        run: echo "Running unit tests"
 
  build-and-push-ecr-image:

    name: Continuous Delivery

    needs: integration

    runs-on: ubuntu-latest

    steps:

      - name: Checkout Code

        uses: actions/checkout@v3
 
      - name: Install Utilities

        run: |

          sudo apt-get update

          sudo apt-get install -y jq unzip

      - name: Configure AWS credentials

        uses: aws-actions/configure-aws-credentials@v1

        with:

          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}

          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

          aws-region: ${{ secrets.AWS_REGION }}
 
      - name: Login to Amazon ECR

        id: login-ecr

        uses: aws-actions/amazon-ecr-login@v1
 
      - name: Build, tag, and push image to Amazon ECR

        id: build-image

        env:

          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}

          ECR_REPOSITORY: ${{ secrets.ECR_REPOSITORY_NAME }}

          IMAGE_TAG: latest

        run: |

          # Build Docker image

          docker compose build

          docker push $ECR_REGISTRY/$ECR_REPOSITORY:backend-latest

          docker push $ECR_REGISTRY/$ECR_REPOSITORY:agent-latest

          # Tag and Push Docker image to ECR

          # docker tag backend $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

          # docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

          # Set output (deprecated but still used by some)

          echo "::set-output name=image::$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG"
 
 
  Continuous-Deployment:

    needs: build-and-push-ecr-image

    runs-on: self-hosted

    steps:

      - name: Checkout

        uses: actions/checkout@v3
 
      - name: Configure AWS credentials

        uses: aws-actions/configure-aws-credentials@v1

        with:

          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}

          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

          aws-region: ${{ secrets.AWS_REGION }}
 
      - name: Login to Amazon ECR

        id: login-ecr

        uses: aws-actions/amazon-ecr-login@v1


      - name: Pull latest images

        run: |

         docker pull ${{ secrets.AWS_ECR_LOGIN_URI }}/${{ secrets.ECR_REPOSITORY_NAME }}:backend-latest

         docker pull ${{ secrets.AWS_ECR_LOGIN_URI }}/${{ secrets.ECR_REPOSITORY_NAME }}:agent-latest

      - name: Stop and remove old containers if running

        run: |

          docker rm -f backend || echo "Backend container not running"

          docker rm -f agent || echo "Agent container not running"
 
 
       

      - name: Run backend container

        run: |

         docker run -d --name backend \

          -p 8000:8000 \

          ${{ secrets.AWS_ECR_LOGIN_URI }}/${{ secrets.ECR_REPOSITORY_NAME }}:backend-latest
 
      - name: Run agent container

        run: |

         docker run -d --name agent \

          --ipc="host" \

          ${{ secrets.AWS_ECR_LOGIN_URI }}/${{ secrets.ECR_REPOSITORY_NAME }}:agent-latest

      - name: Clean previous images and containers

        run: |

         docker system prune -f
 