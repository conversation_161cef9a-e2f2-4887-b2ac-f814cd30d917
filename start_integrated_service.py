#!/usr/bin/env python3
"""
Startup script for the integrated FastAPI + LiveKit Agent service
"""

import os
import sys
import time
import subprocess
from pathlib import Path

def check_requirements():
    """Check if all required files and directories exist"""
    print("🔍 Checking requirements...")
    
    required_files = [
        "langgraph_agent.py",
        "config",
        ".env"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing required files/directories: {missing_files}")
        return False
    
    print("✅ All required files found")
    return True

def setup_environment():
    """Set up the environment"""
    print("🔧 Setting up environment...")
    
    # Create necessary directories
    os.makedirs("call_logs", exist_ok=True)
    os.makedirs("config", exist_ok=True)
    
    print("✅ Environment setup complete")

def start_service():
    """Start the integrated service"""
    print("🚀 Starting integrated FastAPI + LiveKit Agent service...")
    print("=" * 60)
    
    try:
        # Start the integrated service
        process = subprocess.Popen([
            sys.executable, "langgraph_agent.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, bufsize=1)
        
        print("📡 Service starting...")
        print("📍 FastAPI will be available at: http://localhost:8000")
        print("🎙️ LiveKit Agent will be ready for voice calls")
        print("📊 API Documentation: http://localhost:8000/docs")
        print("❤️ Health Check: http://localhost:8000/api/health")
        print()
        print("Press Ctrl+C to stop the service")
        print("=" * 60)
        
        # Stream output in real-time
        try:
            for line in process.stdout:
                print(line.rstrip())
        except KeyboardInterrupt:
            print("\n🛑 Stopping service...")
            process.terminate()
            try:
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                print("⚠️ Force killing service...")
                process.kill()
                process.wait()
            print("✅ Service stopped")
            
    except Exception as e:
        print(f"❌ Failed to start service: {e}")
        return False
    
    return True

def main():
    """Main function"""
    print("🎯 Integrated AI Voice System Startup")
    print("=" * 60)
    
    # Check requirements
    if not check_requirements():
        print("\n❌ Requirements check failed. Please ensure all files are present.")
        return 1
    
    # Setup environment
    setup_environment()
    
    # Start service
    if start_service():
        return 0
    else:
        return 1

if __name__ == "__main__":
    sys.exit(main())
