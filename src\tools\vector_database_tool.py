import sys
import time
from typing import Dict, Any, Optional
from src.rag.retrieval import SearchDocument
from src.exception import CustomException
from src.logging.logger import logging
from src.utils.main_utils import measure_time

class VectorDatabaseTool:
    """
    Dedicated vector database search tool for internal knowledge retrieval.
    This tool searches through stored documents and internal knowledge base.
    """
    
    def __init__(self, llm):
        self.llm = llm
        self.search_doc = SearchDocument(llm=llm)
        self.tool_name = "vector_database_search"
        
        logging.info("Vector Database Tool initialized successfully")

    @measure_time
    def search_documents(self, query: str, language_context: Optional[str] = None) -> Dict[str, Any]:
        """
        Search internal documents using vector database.
        
        Args:
            query: Search query from user
            language_context: Optional language context for better search
            
        Returns:
            Dictionary containing search results and metadata
        """
        start_time = time.time()
        
        try:
            logging.info(f"🔍 Vector DB search for: '{query[:50]}...'")
            
            # Enhance query with language context if provided
            enhanced_query = query
            if language_context and language_context != 'en':
                enhanced_query = f"[{language_context}] {query}"
            
            # Perform vector search
            response = self.search_doc.invoke(enhanced_query)
            
            # Determine if results are relevant
            is_relevant = self._evaluate_relevance(response, query)
            
            search_time = time.time() - start_time
            logging.info(f"Vector DB search completed in {search_time:.2f}s")
            logging.info(f"Results relevant: {is_relevant}")
            
            return {
                "results": response,
                "query": query,
                "enhanced_query": enhanced_query,
                "is_relevant": is_relevant,
                "search_time": search_time,
                "source": "vector_database",
                "language_context": language_context
            }
            
        except Exception as e:
            logging.error(f"Vector database search error: {e}")
            raise CustomException(e, sys)
    
    def _evaluate_relevance(self, response: str, query: str) -> bool:
        """
        Evaluate if the vector database response is relevant to the query.
        
        Args:
            response: Response from vector database
            query: Original user query
            
        Returns:
            Boolean indicating if response is relevant
        """
        if not response:
            return False
            
        # Check for common "no answer" patterns
        no_answer_patterns = [
            "i don't know",
            "no information",
            "not found",
            "unable to find",
            "no relevant",
            "cannot answer"
        ]
        
        response_lower = response.lower()
        for pattern in no_answer_patterns:
            if pattern in response_lower:
                return False
        
        # Check if response has substantial content
        if len(response.strip()) < 20:
            return False
            
        return True
    
    def get_tool_description(self) -> str:
        """Get description for this tool when used in LLM function calling."""
        return """
        Vector Database Search Tool - Use this tool for:
        
        PRIMARY USE CASES:
        - Technical documentation and specifications
        - Internal knowledge base queries
        - Domain-specific information (electrical machines, transformers, etc.)
        - Previously stored documents and manuals
        - Company-specific information
        - Historical data and archived content
        
        WHEN TO USE:
        - Questions about technical specifications
        - Queries about internal processes or documentation
        - Domain expertise questions
        - When you need information from stored documents
        
        WHEN NOT TO USE:
        - Current news or real-time information
        - Recent events or breaking news
        - Live data or current market information
        - Questions requiring up-to-date external information
        """
    
    def get_search_capabilities(self) -> Dict[str, Any]:
        """Get information about search capabilities."""
        return {
            "tool_type": "vector_database",
            "search_method": "semantic_similarity",
            "data_sources": ["internal_documents", "technical_manuals", "knowledge_base"],
            "strengths": [
                "Domain-specific knowledge",
                "Technical documentation",
                "Consistent information",
                "Fast retrieval"
            ],
            "limitations": [
                "No real-time data",
                "Limited to stored documents",
                "May not have latest information"
            ]
        }
