{"timestamp": "2025-08-08T11:49:04.833680", "call_id": "SCL_he2HZmf3ntYY", "event_type": "CALL_STARTED", "data": {"caller": "+916290438432", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-08T11:49:10.031918", "call_id": "SCL_he2HZmf3ntYY", "event_type": "CALL_ENDED", "data": {"duration": 5.197222, "end_time": "2025-08-08T11:49:10.030902"}}
{"timestamp": "2025-08-08T11:51:45.426225", "call_id": "SCL_qkH7WEERVya8", "event_type": "CALL_STARTED", "data": {"caller": "+916290438432", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-08T11:51:49.585262", "call_id": "SCL_qkH7WEERVya8", "event_type": "CALL_ENDED", "data": {"duration": 4.155975, "end_time": "2025-08-08T11:51:49.582200"}}
{"timestamp": "2025-08-08T11:56:51.628182", "call_id": "SCL_LqpVCafzWt7y", "event_type": "CALL_STARTED", "data": {"caller": "+916290438432", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-08T11:56:58.677572", "call_id": "SCL_LqpVCafzWt7y", "event_type": "CALL_ENDED", "data": {"duration": 7.046837, "end_time": "2025-08-08T11:56:58.675019"}}
{"timestamp": "2025-08-08T12:37:08.450721", "call_id": "SCL_fCEbchN6C9pm", "event_type": "CALL_STARTED", "data": {"caller": "+918885161078", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-08T12:37:15.021917", "call_id": "SCL_fCEbchN6C9pm", "event_type": "CALL_ENDED", "data": {"duration": 6.568189, "end_time": "2025-08-08T12:37:15.018910"}}
{"timestamp": "2025-08-08T14:03:17.556115", "call_id": "SCL_5fW9qAZ66Hbh", "event_type": "CALL_STARTED", "data": {"caller": "+918885161078", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-08T14:03:24.048689", "call_id": "SCL_5fW9qAZ66Hbh", "event_type": "CALL_ENDED", "data": {"duration": 6.489714, "end_time": "2025-08-08T14:03:24.045829"}}
{"timestamp": "2025-08-08T14:12:08.712305", "call_id": "SCL_xa9HYLpcFAzq", "event_type": "CALL_STARTED", "data": {"caller": "+918885161078", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-08T14:12:14.427580", "call_id": "SCL_xa9HYLpcFAzq", "event_type": "CALL_ENDED", "data": {"duration": 5.715275, "end_time": "2025-08-08T14:12:14.427580"}}
{"timestamp": "2025-08-08T17:06:12.134020", "call_id": "SCL_9Mp3erosMfBk", "event_type": "CALL_STARTED", "data": {"caller": "+916295716352", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-08T17:06:18.032164", "call_id": "SCL_9Mp3erosMfBk", "event_type": "CALL_ENDED", "data": {"duration": 5.895173, "end_time": "2025-08-08T17:06:18.029193"}}
{"timestamp": "2025-08-08T17:16:07.141961", "call_id": "SCL_drgyUVNdqfxH", "event_type": "CALL_STARTED", "data": {"caller": "+916290438432", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-08T17:16:12.868379", "call_id": "SCL_drgyUVNdqfxH", "event_type": "CALL_ENDED", "data": {"duration": 5.716806, "end_time": "2025-08-08T17:16:12.858767"}}
{"timestamp": "2025-08-08T17:55:04.064354", "call_id": "SCL_W362rfS2NUP6", "event_type": "CALL_STARTED", "data": {"caller": "+916295716352", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-08T17:55:08.072167", "call_id": "SCL_W362rfS2NUP6", "event_type": "CALL_ENDED", "data": {"duration": 4.006886, "end_time": "2025-08-08T17:55:08.071240"}}
