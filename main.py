from multiprocessing import Process
import uvicorn
import os

def start_fastapi():
    uvicorn.run("app:app", host="0.0.0.0", port=8000, reload=False)

def start_agent():
    os.system("python langgraph_agent.py start")

if __name__ == "__main__":
    fastapi_process = Process(target=start_fastapi)
    agent_process = Process(target=start_agent)
    
    fastapi_process.start()
    agent_process.start()
    
    fastapi_process.join()
    agent_process.join()
