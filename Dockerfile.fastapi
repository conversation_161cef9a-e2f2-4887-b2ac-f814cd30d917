# Use slim Python base

FROM python:3.11-slim
 
# Install OS dependencies if needed (e.g., curl, build tools)

RUN apt-get update && apt-get install -y --no-install-recommends \

    curl \
&& apt-get clean \
&& rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*
 
# Set working directory

WORKDIR /app
 
# Copy only requirements first for better Docker caching

COPY requirements.txt .
 
# Install Python dependencies with longer timeout (CI/CD safe)

RUN pip install --no-cache-dir --timeout=100 -r requirements.txt
 
# Copy the rest of your code

COPY . .
 
# Expose the API port

EXPOSE 8000
 
# Default command

CMD ["uvicorn", "app:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

 