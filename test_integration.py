#!/usr/bin/env python3
"""
Test script to verify the frontend-backend integration
"""

import requests
import json
import time
import sys
from pathlib import Path

# Base URL for the FastAPI backend
BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test the health check endpoint"""
    print("🔍 Testing health check...")
    try:
        response = requests.get(f"{BASE_URL}/api/health")
        if response.status_code == 200:
            print("✅ Health check passed")
            print(f"   Response: {response.json()}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_save_settings():
    """Test saving model settings"""
    print("\n🔍 Testing save settings...")
    
    test_settings = {
        "stt": {
            "model": "whisper-large-v3",
            "api_key": "test_stt_key"
        },
        "llm": {
            "model": "llama3-70b-8192",
            "api_key": "test_llm_key"
        },
        "tts": {
            "model": "simba-multilingual",
            "api_key": "test_tts_key"
        }
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/save-settings", json=test_settings)
        if response.status_code == 200:
            print("✅ Settings saved successfully")
            print(f"   Response: {response.json()}")
            return True
        else:
            print(f"❌ Save settings failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Save settings error: {e}")
        return False

def test_load_settings():
    """Test loading model settings"""
    print("\n🔍 Testing load settings...")
    try:
        response = requests.get(f"{BASE_URL}/api/load-settings")
        if response.status_code == 200:
            print("✅ Settings loaded successfully")
            data = response.json()
            if data.get("settings"):
                print(f"   Settings: {json.dumps(data['settings'], indent=2)}")
            else:
                print("   No settings found")
            return True
        else:
            print(f"❌ Load settings failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Load settings error: {e}")
        return False

def test_server_status():
    """Test server status endpoint"""
    print("\n🔍 Testing server status...")
    try:
        response = requests.get(f"{BASE_URL}/api/server-status")
        if response.status_code == 200:
            print("✅ Server status retrieved successfully")
            print(f"   Status: {response.json()}")
            return True
        else:
            print(f"❌ Server status failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Server status error: {e}")
        return False

def test_call_logs():
    """Test call logs endpoint"""
    print("\n🔍 Testing call logs...")
    try:
        response = requests.get(f"{BASE_URL}/api/call-logs")
        if response.status_code == 200:
            print("✅ Call logs retrieved successfully")
            data = response.json()
            print(f"   Found {len(data.get('logs', []))} log files")
            return True
        else:
            print(f"❌ Call logs failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Call logs error: {e}")
        return False

def test_transcriptions():
    """Test transcriptions endpoint"""
    print("\n🔍 Testing transcriptions...")
    try:
        response = requests.get(f"{BASE_URL}/api/transcriptions")
        if response.status_code == 200:
            print("✅ Transcriptions retrieved successfully")
            data = response.json()
            print(f"   Found {len(data.get('transcriptions', []))} transcription files")
            return True
        else:
            print(f"❌ Transcriptions failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Transcriptions error: {e}")
        return False

def test_live_transcription():
    """Test live transcription endpoint"""
    print("\n🔍 Testing live transcription...")
    try:
        response = requests.get(f"{BASE_URL}/api/live-transcription")
        if response.status_code == 200:
            print("✅ Live transcription retrieved successfully")
            data = response.json()
            if data.get('transcription'):
                print("   Live transcription available")
            else:
                print("   No live transcription available")
            return True
        else:
            print(f"❌ Live transcription failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Live transcription error: {e}")
        return False

def main():
    """Run all integration tests"""
    print("🚀 Starting Frontend-Backend Integration Tests")
    print("=" * 60)
    
    tests = [
        test_health_check,
        test_save_settings,
        test_load_settings,
        test_server_status,
        test_call_logs,
        test_transcriptions,
        test_live_transcription
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        time.sleep(0.5)  # Small delay between tests
    
    print("\n" + "=" * 60)
    print(f"🏁 Integration Tests Complete: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! Integration is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the backend server.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
