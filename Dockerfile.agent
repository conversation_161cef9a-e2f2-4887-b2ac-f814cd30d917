FROM python:3.11-slim
 
# Install OS dependencies and clean up
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
&& apt-get clean \
&& rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*
 
# Set working directory
WORKDIR /app
 
# Copy and install dependencies first for better layer caching
COPY requirements.txt .
 
# Optional: Increase pip timeout for CI/CD reliability
RUN pip install --no-cache-dir --timeout=100 -r requirements.txt
 
# Copy the full application source
COPY . .
 
# Optional: Only run download step if it's safe during build
# If the download fails due to network, don't break the build
RUN python langgraph-agent.py download-files || echo "Skipping download"
 
# Define entrypoint and default command
ENTRYPOINT ["python", "langgraph-agent.py"]
CMD ["start"]