{"cells": [{"cell_type": "markdown", "id": "5d30a28a", "metadata": {}, "source": ["# Document Loader"]}, {"cell_type": "code", "execution_count": 21, "id": "74119cff", "metadata": {}, "outputs": [], "source": ["from langchain_community.document_loaders import PyPDFLoader, TextLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from typing import List, Optional\n", "from langchain.schema import Document\n", "from pathlib import Path"]}, {"cell_type": "code", "execution_count": 16, "id": "ebcf9afb", "metadata": {}, "outputs": [], "source": ["file_path= r\"D:\\GENAIProjects\\calling-repasentive-ai\\calling-agent\\data\""]}, {"cell_type": "code", "execution_count": 22, "id": "6f564908", "metadata": {}, "outputs": [], "source": ["text_splitter = RecursiveCharacterTextSplitter(\n", "            chunk_size = 1024,\n", "            chunk_overlap = 100,\n", "            length_function=len\n", "        )"]}, {"cell_type": "code", "execution_count": 29, "id": "4c842f47", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Skipping unsupported file type: multimodal.txt\n"]}], "source": ["dir_path = Path(file_path)\n", "all_documents = []\n", "\n", "for file in dir_path.iterdir():\n", "    if file.suffix == '.pdf':\n", "        loader = PyPDFLoader(str(file))\n", "  \n", "    else:\n", "        print(f\"Skipping unsupported file type: {file.name}\")\n", "        continue"]}, {"cell_type": "code", "execution_count": 31, "id": "924f1ccc", "metadata": {}, "outputs": [], "source": ["docs = loader.load()"]}, {"cell_type": "code", "execution_count": 35, "id": "41fa0a48", "metadata": {}, "outputs": [{"data": {"text/plain": ["Document(metadata={'producer': 'pdfTeX-1.40.21', 'creator': 'LaTeX with hyperref', 'creationdate': '2021-04-13T00:48:38+00:00', 'author': '', 'keywords': '', 'moddate': '2021-04-13T00:48:38+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.14159265-2.6-1.40.21 (TeX Live 2020) kpathsea version 6.3.2', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'D:\\\\GENAIProjects\\\\calling-repasentive-ai\\\\calling-agent\\\\data\\\\RAG_arxiv.pdf', 'total_pages': 19, 'page': 1, 'page_label': '2'}, page_content='The\\tDivine\\nComedy\\t(x) q \\nQuery \\nEncoder \\nq(x) \\nMIPS p θ \\nGenerator\\xa0pθ\\n(Parametric) \\nMargin- \\nalize \\nThis\\t14th\\tcentury\\twork\\nis\\tdivided\\tinto\\t3\\nsections:\\t\"Inferno\",\\n\"Purgatorio\"\\t&\\n\"Paradiso\"\\t\\t\\t\\t\\t\\t\\t\\t\\t(y)\\nEnd-to-End Backprop through q  and\\xa0p θ \\nBarack\\tObama\\twas\\nborn\\tin\\tHawaii.(x)\\nFact Veriﬁcation: Fact Query\\nsupports\\t(y)\\nQuestion Generation\\nFact Veriﬁcation:\\nLabel Generation\\nDocument \\nIndex \\nDefine\\t\"middle\\tear\"(x)\\nQuestion Answering:\\nQuestion Query\\nThe\\tmiddle\\tear\\tincludes\\nthe\\ttympanic\\tcavity\\tand\\nthe\\tthree\\tossicles.\\t\\t(y)\\nQuestion Answering:\\nAnswer GenerationRetriever pη \\n(Non-Parametric) \\nz 4 \\nz 3 \\nz 2 \\nz 1 \\nd(z) \\nJeopardy Question\\nGeneration:\\nAnswer Query\\nFigure 1: Overview of our approach. We combine a pre-trained retriever (Query Encoder + Document\\nIndex) with a pre-trained seq2seq model (Generator) and ﬁne-tune end-to-end. For query x, we use\\nMaximum Inner Product Search (MIPS) to ﬁnd the top-K documents zi. For ﬁnal prediction y, we\\ntreat zas a latent variable and marginalize over seq2seq predictions given different documents.\\nbut have only explored open-domain extractive question answering. Here, we bring hybrid parametric\\nand non-parametric memory to the “workhorse of NLP,” i.e. sequence-to-sequence (seq2seq) models.\\nWe endow pre-trained, parametric-memory generation models with a non-parametric memory through\\na general-purpose ﬁne-tuning approach which we refer to as retrieval-augmented generation (RAG).\\nWe build RAG models where the parametric memory is a pre-trained seq2seq transformer, and the\\nnon-parametric memory is a dense vector index of Wikipedia, accessed with a pre-trained neural\\nretriever. We combine these components in a probabilistic model trained end-to-end (Fig. 1). The\\nretriever (Dense Passage Retriever [26], henceforth DPR) provides latent documents conditioned on\\nthe input, and the seq2seq model (BART [32]) then conditions on these latent documents together with\\nthe input to generate the output. We marginalize the latent documents with a top-K approximation,\\neither on a per-output basis (assuming the same document is responsible for all tokens) or a per-token\\nbasis (where different documents are responsible for different tokens). Like T5 [51] or BART, RAG\\ncan be ﬁne-tuned on any seq2seq task, whereby both the generator and retriever are jointly learned.\\nThere has been extensive previous work proposing architectures to enrich systems with non-parametric\\nmemory which are trained from scratch for speciﬁc tasks, e.g. memory networks [ 64, 55], stack-\\naugmented networks [25] and memory layers [ 30]. In contrast, we explore a setting where both\\nparametric and non-parametric memory components are pre-trained and pre-loaded with extensive\\nknowledge. Crucially, by using pre-trained access mechanisms, the ability to access knowledge is\\npresent without additional training.\\nOur results highlight the beneﬁts of combining parametric and non-parametric memory with genera-\\ntion for knowledge-intensive tasks—tasks that humans could not reasonably be expected to perform\\nwithout access to an external knowledge source. Our RAG models achieve state-of-the-art results\\non open Natural Questions [29], WebQuestions [3] and CuratedTrec [2] and strongly outperform\\nrecent approaches that use specialised pre-training objectives on TriviaQA [24]. Despite these being\\nextractive tasks, we ﬁnd that unconstrained generation outperforms previous extractive approaches.\\nFor knowledge-intensive generation, we experiment with MS-MARCO [1] and Jeopardy question\\ngeneration, and we ﬁnd that our models generate responses that are more factual, speciﬁc, and\\ndiverse than a BART baseline. For FEVER [56] fact veriﬁcation, we achieve results within 4.3% of\\nstate-of-the-art pipeline models which use strong retrieval supervision. Finally, we demonstrate that\\nthe non-parametric memory can be replaced to update the models’ knowledge as the world changes.1\\n2 Methods\\nWe explore RAG models, which use the input sequencexto retrieve text documents zand use them\\nas additional context when generating the target sequence y. As shown in Figure 1, our models\\nleverage two components: (i) a retriever pη(z|x) with parameters ηthat returns (top-K truncated)\\ndistributions over text passages given a query xand (ii) a generator pθ(yi|x,z,y 1:i−1) parametrized\\n1Code to run experiments with RAG has been open-sourced as part of the HuggingFace Transform-\\ners Library [66] and can be found at https://github.com/huggingface/transformers/blob/master/\\nexamples/rag/. An interactive demo of RAG models can be found at https://huggingface.co/rag/\\n2')"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["docs[1]"]}, {"cell_type": "code", "execution_count": 33, "id": "4193bd5c", "metadata": {}, "outputs": [], "source": ["all_documents = []\n", "for doc in docs:\n", "    src = doc.metadata.get(\"source\", str(file.name))\n", "    all_documents.append(\n", "        Document(\n", "            page_content=doc.page_content,\n", "            metadata={'source': src}\n", "        )\n", "    )"]}, {"cell_type": "code", "execution_count": 34, "id": "dd64d457", "metadata": {}, "outputs": [{"data": {"text/plain": ["Document(metadata={'source': 'D:\\\\GENAIProjects\\\\calling-repasentive-ai\\\\calling-agent\\\\data\\\\RAG_arxiv.pdf'}, page_content='The\\tDivine\\nComedy\\t(x) q \\nQuery \\nEncoder \\nq(x) \\nMIPS p θ \\nGenerator\\xa0pθ\\n(Parametric) \\nMargin- \\nalize \\nThis\\t14th\\tcentury\\twork\\nis\\tdivided\\tinto\\t3\\nsections:\\t\"Inferno\",\\n\"Purgatorio\"\\t&\\n\"Paradiso\"\\t\\t\\t\\t\\t\\t\\t\\t\\t(y)\\nEnd-to-End Backprop through q  and\\xa0p θ \\nBarack\\tObama\\twas\\nborn\\tin\\tHawaii.(x)\\nFact Veriﬁcation: Fact Query\\nsupports\\t(y)\\nQuestion Generation\\nFact Veriﬁcation:\\nLabel Generation\\nDocument \\nIndex \\nDefine\\t\"middle\\tear\"(x)\\nQuestion Answering:\\nQuestion Query\\nThe\\tmiddle\\tear\\tincludes\\nthe\\ttympanic\\tcavity\\tand\\nthe\\tthree\\tossicles.\\t\\t(y)\\nQuestion Answering:\\nAnswer GenerationRetriever pη \\n(Non-Parametric) \\nz 4 \\nz 3 \\nz 2 \\nz 1 \\nd(z) \\nJeopardy Question\\nGeneration:\\nAnswer Query\\nFigure 1: Overview of our approach. We combine a pre-trained retriever (Query Encoder + Document\\nIndex) with a pre-trained seq2seq model (Generator) and ﬁne-tune end-to-end. For query x, we use\\nMaximum Inner Product Search (MIPS) to ﬁnd the top-K documents zi. For ﬁnal prediction y, we\\ntreat zas a latent variable and marginalize over seq2seq predictions given different documents.\\nbut have only explored open-domain extractive question answering. Here, we bring hybrid parametric\\nand non-parametric memory to the “workhorse of NLP,” i.e. sequence-to-sequence (seq2seq) models.\\nWe endow pre-trained, parametric-memory generation models with a non-parametric memory through\\na general-purpose ﬁne-tuning approach which we refer to as retrieval-augmented generation (RAG).\\nWe build RAG models where the parametric memory is a pre-trained seq2seq transformer, and the\\nnon-parametric memory is a dense vector index of Wikipedia, accessed with a pre-trained neural\\nretriever. We combine these components in a probabilistic model trained end-to-end (Fig. 1). The\\nretriever (Dense Passage Retriever [26], henceforth DPR) provides latent documents conditioned on\\nthe input, and the seq2seq model (BART [32]) then conditions on these latent documents together with\\nthe input to generate the output. We marginalize the latent documents with a top-K approximation,\\neither on a per-output basis (assuming the same document is responsible for all tokens) or a per-token\\nbasis (where different documents are responsible for different tokens). Like T5 [51] or BART, RAG\\ncan be ﬁne-tuned on any seq2seq task, whereby both the generator and retriever are jointly learned.\\nThere has been extensive previous work proposing architectures to enrich systems with non-parametric\\nmemory which are trained from scratch for speciﬁc tasks, e.g. memory networks [ 64, 55], stack-\\naugmented networks [25] and memory layers [ 30]. In contrast, we explore a setting where both\\nparametric and non-parametric memory components are pre-trained and pre-loaded with extensive\\nknowledge. Crucially, by using pre-trained access mechanisms, the ability to access knowledge is\\npresent without additional training.\\nOur results highlight the beneﬁts of combining parametric and non-parametric memory with genera-\\ntion for knowledge-intensive tasks—tasks that humans could not reasonably be expected to perform\\nwithout access to an external knowledge source. Our RAG models achieve state-of-the-art results\\non open Natural Questions [29], WebQuestions [3] and CuratedTrec [2] and strongly outperform\\nrecent approaches that use specialised pre-training objectives on TriviaQA [24]. Despite these being\\nextractive tasks, we ﬁnd that unconstrained generation outperforms previous extractive approaches.\\nFor knowledge-intensive generation, we experiment with MS-MARCO [1] and Jeopardy question\\ngeneration, and we ﬁnd that our models generate responses that are more factual, speciﬁc, and\\ndiverse than a BART baseline. For FEVER [56] fact veriﬁcation, we achieve results within 4.3% of\\nstate-of-the-art pipeline models which use strong retrieval supervision. Finally, we demonstrate that\\nthe non-parametric memory can be replaced to update the models’ knowledge as the world changes.1\\n2 Methods\\nWe explore RAG models, which use the input sequencexto retrieve text documents zand use them\\nas additional context when generating the target sequence y. As shown in Figure 1, our models\\nleverage two components: (i) a retriever pη(z|x) with parameters ηthat returns (top-K truncated)\\ndistributions over text passages given a query xand (ii) a generator pθ(yi|x,z,y 1:i−1) parametrized\\n1Code to run experiments with RAG has been open-sourced as part of the HuggingFace Transform-\\ners Library [66] and can be found at https://github.com/huggingface/transformers/blob/master/\\nexamples/rag/. An interactive demo of RAG models can be found at https://huggingface.co/rag/\\n2')"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["all_documents[1]"]}, {"cell_type": "code", "execution_count": 27, "id": "b6bb37a7", "metadata": {}, "outputs": [], "source": ["def load_document(file_path: str) -> Optional[List[Document]]:\n", "    \"\"\"\n", "    Loads documents from a directory and splits them into chunks.\n", "\n", "    Args:\n", "        file_path: Path to a directory containing document files\n", "\n", "    Returns:\n", "        List of document chunks or None if loading fails\n", "    \"\"\"\n", "    try:\n", "        dir_path = Path(file_path)\n", "        all_documents = []\n", "\n", "        for file in dir_path.iterdir():\n", "            if file.suffix == '.pdf':\n", "                loader = PyPDFLoader(str(file))\n", "          \n", "            else:\n", "                print(f\"Skipping unsupported file type: {file.name}\")\n", "                continue\n", "\n", "            # Load the document\n", "            docs = loader.load()\n", "\n", "            # Add metadata and wrap in Document schema\n", "            for doc in docs:\n", "                src = doc.metadata.get(\"source\", str(file.name))\n", "                all_documents.append(\n", "                    Document(\n", "                        page_content=doc.page_content,\n", "                        metadata={'source': src}\n", "                    )\n", "                )\n", "\n", "        if not all_documents:\n", "            print(f\"No valid documents found in '{file_path}'\")\n", "            return None\n", "\n", "        # Split into chunks\n", "        doc_chunks = text_splitter.split_documents(all_documents)\n", "\n", "        if not doc_chunks:\n", "            print(f\"No chunks generated from '{file_path}'.\")\n", "            return None\n", "\n", "        print(f\"Loaded documents from '{file_path}': {len(doc_chunks)} chunks.\")\n", "        return doc_chunks\n", "\n", "    except Exception as e:\n", "        print(f\"Failed to load documents from '{file_path}': {e}\")\n", "        return None\n"]}, {"cell_type": "code", "execution_count": 42, "id": "a97bd5ef", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Skipping unsupported file type: multimodal.txt\n", "Loaded documents from 'D:\\GENAIProjects\\calling-repasentive-ai\\calling-agent\\data': 101 chunks.\n"]}], "source": ["doc_chunks= load_document(file_path=file_path)"]}, {"cell_type": "markdown", "id": "bdfe8368", "metadata": {}, "source": ["# Embedding Load"]}, {"cell_type": "code", "execution_count": 38, "id": "61b01a0a", "metadata": {}, "outputs": [], "source": ["from langchain_huggingface import HuggingFaceEmbeddings\n", "\n", "def load_embedings():\n", "        \"\"\"\n", "        Download and return the HuggingFace embeddings model.\n", "        \"\"\"\n", "        model_kwargs = {'device': 'cpu'}\n", "        encode_kwargs = {'normalize_embeddings': False}\n", "        embeddings = HuggingFaceEmbeddings(\n", "            model_name='sentence-transformers/all-MiniLM-L6-v2',\n", "            model_kwargs=model_kwargs,\n", "            encode_kwargs=encode_kwargs\n", "        )\n", "        # Embedding dimension\n", "        dim = len(embeddings.embed_query(\"hello\"))\n", "        return embeddings, dim"]}, {"cell_type": "code", "execution_count": 39, "id": "2c26c5a3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\GENAIProjects\\calling-repasentive-ai\\calling-agent\\.venv\\Lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["embedings,dim=load_embedings()"]}, {"cell_type": "code", "execution_count": 40, "id": "fcb320df", "metadata": {}, "outputs": [{"data": {"text/plain": ["384"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["dim"]}, {"cell_type": "markdown", "id": "59b9563a", "metadata": {}, "source": ["# Vectore Store"]}, {"cell_type": "code", "execution_count": 63, "id": "4a9148fe", "metadata": {}, "outputs": [], "source": ["import os\n", "import getpass\n", "if not os.getenv(\"PINECONE_API_KEY\"):\n", "    os.environ[\"PINECONE_API_KEY\"] = getpass.getpass(\"Enter your Pinecone API key: \")"]}, {"cell_type": "code", "execution_count": 64, "id": "d6d3fc34", "metadata": {}, "outputs": [], "source": ["from pinecone import Pinecone\n", "pinecone_api_key = os.environ.get(\"PINECONE_API_KEY\")\n", "\n", "pc = Pinecone(api_key=pinecone_api_key)"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [], "source": ["from pinecone import ServerlessSpec\n", "\n", "index_name = \"langchain-test-index\"  # change if desired\n", "\n", "if not pc.has_index(index_name):\n", "    pc.create_index(\n", "        name=index_name,\n", "        dimension=dim,\n", "        metric=\"cosine\",\n", "        spec=ServerlessSpec(cloud=\"aws\", region=\"us-east-1\"),\n", "    )\n", "\n", "index = pc.Index(index_name)"]}, {"cell_type": "code", "execution_count": 66, "id": "76a34d5a", "metadata": {}, "outputs": [], "source": ["from langchain_pinecone import PineconeVectorStore\n", "\n", "vector_store = PineconeVectorStore(index=index, embedding=embedings)"]}, {"cell_type": "code", "execution_count": 69, "id": "3dc7058e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<langchain_pinecone.vectorstores.PineconeVectorStore at 0x239015440d0>"]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["vector_store.from_documents(\n", "    documents=doc_chunks,\n", "    index_name= index_name,\n", "    embedding=embedings\n", ")"]}, {"cell_type": "markdown", "id": "7933ea35", "metadata": {}, "source": ["## Dense Vector Store"]}, {"cell_type": "code", "execution_count": null, "id": "af3c9883", "metadata": {}, "outputs": [], "source": ["import getpass\n", "if not os.getenv(\"PINECONE_API_KEY\"):\n", "    os.environ[\"PINECONE_API_KEY\"] = getpass.getpass(\"Enter your Pinecone API key: \")\n", "    \n", "pinecone_api_key = os.environ.get(\"PINECONE_API_KEY\")"]}, {"cell_type": "code", "execution_count": 86, "id": "7c547ece", "metadata": {}, "outputs": [], "source": ["from langchain_pinecone import PineconeSparseVectorStore\n", "from pinecone import Pinecone, ServerlessSpec\n", "\n", "def vectore_store_dense(embeddings, embeddings_dim, doc_chunks):\n", "\ttry:\n", "\t\tindex_name = \"agentic-rag\"\n", "\t\tpc = Pinecone(api_key=pinecone_api_key)\n", "\n", "\t\tif not pc.has_index(index_name):\n", "\t\t\tpc.create_index(\n", "\t\t\t\tname=index_name,\n", "\t\t\t\tdimension=embeddings_dim,\n", "\t\t\t\tmetric=\"cosine\",\n", "\t\t\t\tspec=ServerlessSpec(cloud=\"aws\", region=\"us-east-1\"),\n", "\t\t\t)\n", "\n", "\t\tindex = pc.Index(index_name)\n", "\n", "\t\tvector_store = PineconeVectorStore(index=index, embedding=embeddings)\n", "\t\tvector_store.from_documents(\n", "\t\t\tdocuments=doc_chunks,\n", "\t\t\tindex_name=index_name,\n", "\t\t\tembedding=embeddings\n", "\t\t)\n", "\n", "\t\tprint(\"PinecodeDB vector store setup Completed.\")\n", "\t\treturn vector_store\n", "\n", "\texcept Exception as e:\n", "\t\tprint(f\"Error in vector DB: {e}\")\n", "\t\traise e\n", "    "]}, {"cell_type": "code", "execution_count": 87, "id": "fd9bab94", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PinecodeDB vector store setup Completed.\n"]}], "source": ["dense_vector_store=vectore_store_dense(\n", "    embeddings=embedings,\n", "    embeddings_dim=dim,\n", "    doc_chunks=doc_chunks\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a674fa7d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "c4d9ec27", "metadata": {}, "source": ["## Sparse Vector Store"]}, {"cell_type": "code", "execution_count": 78, "id": "23c5de20", "metadata": {}, "outputs": [], "source": ["from langchain_pinecone import PineconeSparseVectorStore\n", "from pinecone import Pinecone, ServerlessSpec\n", "from typing import Any\n", "import os\n", "\n", "def vectore_store_sparse(embeddings: Any, embeddings_dim: int, doc_chunks: list):\n", "    try:\n", "        index_name = \"agentic-rag\"\n", "        pc = Pinecone(api_key=pinecone_api_key)\n", "\n", "        # Create index if it doesn't exist\n", "        if not pc.list_indexes() or index_name not in pc.list_indexes().names():\n", "            pc.create_index(\n", "                name=index_name,\n", "                dimension=embeddings_dim,\n", "                metric=\"cosine\",\n", "                spec=ServerlessSpec(cloud=\"aws\", region=\"us-east-1\"),\n", "            )\n", "\n", "        index = pc.Index(index_name)\n", "\n", "        # ❗Correct usage of `from_documents` (classmethod)\n", "        vector_store = PineconeSparseVectorStore.from_documents(\n", "            documents=doc_chunks,\n", "            embedding=embeddings,\n", "            index_name=index_name\n", "        )\n", "\n", "        print(\"✅ Pinecone sparse vector store setup completed.\")\n", "\n", "        return vector_store\n", "\n", "    except Exception as e:\n", "        print(f\"❌ Error in vector DB setup: {e}\")\n", "        raise\n"]}, {"cell_type": "code", "execution_count": 75, "id": "30e12b61", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["No sentence-transformers model found with name naver/splade-cocondenser-ensembledistil. Creating a new one with mean pooling.\n", "Some weights of BertModel were not initialized from the model checkpoint at naver/splade-cocondenser-ensembledistil and are newly initialized: ['pooler.dense.bias', 'pooler.dense.weight']\n", "You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.\n"]}], "source": ["model_kwargs = {'device': 'cpu'}\n", "encode_kwargs = {'normalize_embeddings': False}\n", "embeddings_sparse = HuggingFaceEmbeddings(\n", "            model_name=\"naver/splade-cocondenser-ensembledistil\",\n", "            model_kwargs=model_kwargs,\n", "            encode_kwargs=encode_kwargs\n", "        )"]}, {"cell_type": "code", "execution_count": 76, "id": "5b9f6f77", "metadata": {}, "outputs": [], "source": ["splade_model_dim =len(embeddings_sparse.embed_query(\"hello\"))"]}, {"cell_type": "code", "execution_count": 81, "id": "8bc85989", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["❌ Error in vector DB setup: PineconeSparseVectorStore can only be used with Sparse Indexes\n"]}, {"ename": "ValueError", "evalue": "PineconeSparseVectorStore can only be used with Sparse Indexes", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[81]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[43mvectore_store_sparse\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m      2\u001b[39m \u001b[43m    \u001b[49m\u001b[43membeddings\u001b[49m\u001b[43m=\u001b[49m\u001b[43membedings\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m      3\u001b[39m \u001b[43m    \u001b[49m\u001b[43membeddings_dim\u001b[49m\u001b[43m=\u001b[49m\u001b[43msplade_model_dim\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m      4\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdoc_chunks\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdoc_chunks\u001b[49m\n\u001b[32m      5\u001b[39m \u001b[43m)\u001b[49m\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[78]\u001b[39m\u001b[32m, line 23\u001b[39m, in \u001b[36mvectore_store_sparse\u001b[39m\u001b[34m(embeddings, embeddings_dim, doc_chunks)\u001b[39m\n\u001b[32m     20\u001b[39m index = pc.Index(index_name)\n\u001b[32m     22\u001b[39m \u001b[38;5;66;03m# ❗Correct usage of `from_documents` (classmethod)\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m23\u001b[39m vector_store = \u001b[43mPineconeSparseVectorStore\u001b[49m\u001b[43m.\u001b[49m\u001b[43mfrom_documents\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m     24\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdocuments\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdoc_chunks\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     25\u001b[39m \u001b[43m    \u001b[49m\u001b[43membedding\u001b[49m\u001b[43m=\u001b[49m\u001b[43membeddings\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     26\u001b[39m \u001b[43m    \u001b[49m\u001b[43mindex_name\u001b[49m\u001b[43m=\u001b[49m\u001b[43mindex_name\u001b[49m\n\u001b[32m     27\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     29\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m✅ Pinecone sparse vector store setup completed.\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     31\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m vector_store\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\GENAIProjects\\calling-repasentive-ai\\calling-agent\\.venv\\Lib\\site-packages\\langchain_core\\vectorstores\\base.py:848\u001b[39m, in \u001b[36mVectorStore.from_documents\u001b[39m\u001b[34m(cls, documents, embedding, **kwargs)\u001b[39m\n\u001b[32m    845\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28many\u001b[39m(ids):\n\u001b[32m    846\u001b[39m         kwargs[\u001b[33m\"\u001b[39m\u001b[33mids\u001b[39m\u001b[33m\"\u001b[39m] = ids\n\u001b[32m--> \u001b[39m\u001b[32m848\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mcls\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mfrom_texts\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtexts\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43membedding\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmetadatas\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmetadatas\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\GENAIProjects\\calling-repasentive-ai\\calling-agent\\.venv\\Lib\\site-packages\\langchain_pinecone\\vectorstores.py:846\u001b[39m, in \u001b[36mPineconeVectorStore.from_texts\u001b[39m\u001b[34m(cls, texts, embedding, metadatas, ids, batch_size, text_key, namespace, index_name, upsert_kwargs, pool_threads, embeddings_chunk_size, async_req, id_prefix, **kwargs)\u001b[39m\n\u001b[32m    818\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"Construct Pinecone wrapper from raw documents.\u001b[39;00m\n\u001b[32m    819\u001b[39m \n\u001b[32m    820\u001b[39m \u001b[33;03mThis is a user-friendly interface that:\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    843\u001b[39m \u001b[33;03m        )\u001b[39;00m\n\u001b[32m    844\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m    845\u001b[39m pinecone_index = \u001b[38;5;28mcls\u001b[39m.get_pinecone_index(index_name, pool_threads)\n\u001b[32m--> \u001b[39m\u001b[32m846\u001b[39m pinecone = \u001b[38;5;28;43mcls\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mpinecone_index\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43membedding\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtext_key\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnamespace\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    848\u001b[39m pinecone.add_texts(\n\u001b[32m    849\u001b[39m     texts,\n\u001b[32m    850\u001b[39m     metadatas=metadatas,\n\u001b[32m   (...)\u001b[39m\u001b[32m    857\u001b[39m     **(upsert_kwargs \u001b[38;5;129;01mor\u001b[39;00m {}),\n\u001b[32m    858\u001b[39m )\n\u001b[32m    859\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m pinecone\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\GENAIProjects\\calling-repasentive-ai\\calling-agent\\.venv\\Lib\\site-packages\\langchain_pinecone\\vectorstores_sparse.py:187\u001b[39m, in \u001b[36mPineconeSparseVectorStore.__init__\u001b[39m\u001b[34m(self, index, embedding, text_key, namespace, distance_strategy, pinecone_api_key, index_name)\u001b[39m\n\u001b[32m    175\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m__init__\u001b[39m(\n\u001b[32m    176\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m    177\u001b[39m     index: Optional[Any] = \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m   (...)\u001b[39m\u001b[32m    184\u001b[39m     index_name: Optional[\u001b[38;5;28mstr\u001b[39m] = \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m    185\u001b[39m ):\n\u001b[32m    186\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m index \u001b[38;5;129;01mand\u001b[39;00m index.describe_index_stats()[\u001b[33m\"\u001b[39m\u001b[33mvector_type\u001b[39m\u001b[33m\"\u001b[39m] != \u001b[33m\"\u001b[39m\u001b[33msparse\u001b[39m\u001b[33m\"\u001b[39m:\n\u001b[32m--> \u001b[39m\u001b[32m187\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[32m    188\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mPineconeSparseVectorStore can only be used with Sparse Indexes\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    189\u001b[39m         )\n\u001b[32m    190\u001b[39m     \u001b[38;5;28msuper\u001b[39m().\u001b[34m__init__\u001b[39m(\n\u001b[32m    191\u001b[39m         index,\n\u001b[32m    192\u001b[39m         embedding,\n\u001b[32m   (...)\u001b[39m\u001b[32m    197\u001b[39m         index_name=index_name,\n\u001b[32m    198\u001b[39m     )\n", "\u001b[31mValueError\u001b[39m: PineconeSparseVectorStore can only be used with Sparse Indexes"]}], "source": ["vectore_store_sparse(\n", "    embeddings=embedings,\n", "    embeddings_dim=splade_model_dim,\n", "    doc_chunks=doc_chunks\n", ")"]}, {"cell_type": "markdown", "id": "50c29c4f", "metadata": {}, "source": ["# Retriever"]}, {"cell_type": "code", "execution_count": 85, "id": "0c6060ac", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'NoneType' object has no attribute 'similarity_search'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[85]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[43mdense_vector_store\u001b[49m\u001b[43m.\u001b[49m\u001b[43msimilarity_search\u001b[49m(\n\u001b[32m      2\u001b[39m \t\u001b[33m\"\u001b[39m\u001b[33mType Switchgear Transformer\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m      3\u001b[39m     k =\u001b[32m2\u001b[39m\n\u001b[32m      4\u001b[39m )\n", "\u001b[31mAttributeError\u001b[39m: 'NoneType' object has no attribute 'similarity_search'"]}], "source": ["dense_vector_store.similarity_search(\n", "\t\"Type Switchgear Transformer\",\n", "    k =2\n", ")"]}, {"cell_type": "code", "execution_count": 92, "id": "d2df0355", "metadata": {}, "outputs": [], "source": ["results=dense_vector_store.similarity_search(\"Type Switchgear Transformer\")"]}, {"cell_type": "code", "execution_count": 93, "id": "b9dc9d8b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* 35\n", "nkt cables SWITCHGEAR & TRANSFORMER TERMINATION Underground Transmission Solutions \n", "Cable Accessories\n", "Type Switchgear Transformer KSEV 72  \n", "KTEV 72\n", "KSEV 145  \n", "KTEV 145\n", "KSEV 245  \n", "KTEV 245\n", "Operation Voltage Um (kV) 72 145 245\n", "Conductor <PERSON>u/Al  Max. (kcmil) 2000 2500 5000\n", "Diameter Over Dielectric Min.-Max. (inches) 1.5-2.9 1.5-2.9 1.8-3.9\n", "Diameter Over Jacket Max. (inches) 3.9 3.9 5.3\n", "Length L1 (mm) 310 470 620\n", "Length L2 (mm) 582 757 960\n", "D1 (mm) 245 266 475\n", "D2 (mm) 300 350 500\n", "nkt cables \n", "Switchgear & \n", "Transformer  \n", "Termination \n", "KSEV/KTEV DRY TYPE PLUG-IN TERMINATION UP TO 245 kV\n", "All versions of dry-type termination are designed for installation in SF6 gas insulated \n", "switchgear (GIS) or for installation in the oil filled cable box of the transformer. The \n", "complete termination consists of epoxy resin insulator with embedded electrode, \n", "fixing ring which is fitted to the cable, comprising metal cable gland, compression \n", "device, and premolded plug-in stress cone for electrical field control. [{'source': 'D:\\\\GENAIProjects\\\\calling-repasentive-ai\\\\calling-agent\\\\data\\\\HighVoltag.pdf'}]\n", "* 35\n", "nkt cables SWITCHGEAR & TRANSFORMER TERMINATION Underground Transmission Solutions \n", "Cable Accessories\n", "Type Switchgear Transformer KSEV 72  \n", "KTEV 72\n", "KSEV 145  \n", "KTEV 145\n", "KSEV 245  \n", "KTEV 245\n", "Operation Voltage Um (kV) 72 145 245\n", "Conductor <PERSON>u/Al  Max. (kcmil) 2000 2500 5000\n", "Diameter Over Dielectric Min.-Max. (inches) 1.5-2.9 1.5-2.9 1.8-3.9\n", "Diameter Over Jacket Max. (inches) 3.9 3.9 5.3\n", "Length L1 (mm) 310 470 620\n", "Length L2 (mm) 582 757 960\n", "D1 (mm) 245 266 475\n", "D2 (mm) 300 350 500\n", "nkt cables \n", "Switchgear & \n", "Transformer  \n", "Termination \n", "KSEV/KTEV DRY TYPE PLUG-IN TERMINATION UP TO 245 kV\n", "All versions of dry-type termination are designed for installation in SF6 gas insulated \n", "switchgear (GIS) or for installation in the oil filled cable box of the transformer. The \n", "complete termination consists of epoxy resin insulator with embedded electrode, \n", "fixing ring which is fitted to the cable, comprising metal cable gland, compression \n", "device, and premolded plug-in stress cone for electrical field control. [{'source': 'D:\\\\GENAIProjects\\\\calling-repasentive-ai\\\\calling-agent\\\\data\\\\HighVoltag.pdf'}]\n", "* 35\n", "nkt cables SWITCHGEAR & TRANSFORMER TERMINATION Underground Transmission Solutions \n", "Cable Accessories\n", "Type Switchgear Transformer KSEV 72  \n", "KTEV 72\n", "KSEV 145  \n", "KTEV 145\n", "KSEV 245  \n", "KTEV 245\n", "Operation Voltage Um (kV) 72 145 245\n", "Conductor <PERSON>u/Al  Max. (kcmil) 2000 2500 5000\n", "Diameter Over Dielectric Min.-Max. (inches) 1.5-2.9 1.5-2.9 1.8-3.9\n", "Diameter Over Jacket Max. (inches) 3.9 3.9 5.3\n", "Length L1 (mm) 310 470 620\n", "Length L2 (mm) 582 757 960\n", "D1 (mm) 245 266 475\n", "D2 (mm) 300 350 500\n", "nkt cables \n", "Switchgear & \n", "Transformer  \n", "Termination \n", "KSEV/KTEV DRY TYPE PLUG-IN TERMINATION UP TO 245 kV\n", "All versions of dry-type termination are designed for installation in SF6 gas insulated \n", "switchgear (GIS) or for installation in the oil filled cable box of the transformer. The \n", "complete termination consists of epoxy resin insulator with embedded electrode, \n", "fixing ring which is fitted to the cable, comprising metal cable gland, compression \n", "device, and premolded plug-in stress cone for electrical field control. [{'source': 'D:\\\\GENAIProjects\\\\calling-repasentive-ai\\\\calling-agent\\\\data\\\\HighVoltag.pdf'}]\n", "* 36\n", "Underground Transmission Solutions \n", "Cable AccessoriesABB SWITCHGEAR & TRANSFORMER TERMINATION\n", "ABB Switchgear \n", "& Transformer  \n", "Termination \n", "APEGA OIL FILLED PLUG IN TERMINATION FOR GAS INSULATED \n", "SWITCHGEAR AND TRANSFORMER UP TO 420 kV\n", "Oil filled cable termination suitable as a fixed connection point in a gas-insulated \n", "switchgear, a transformer without a separate cable box, or where the cable box is filled \n", "with transformer oil.\n", "The cable termination consists of an epoxy insulator fitted to a box body made of \n", "aluminum. The stress controlling component is a rubber stress cone. The insulator is \n", "filled with synthetic insulating oil. A flange for insulated installation is integrated in the \n", "epoxy insulator. A pressure ring is also included.\n", "•  CST Corona shield for transformer (TRF) applications, made of aluminum with surface \n", "insulation coating\n", "• CBT Contact bolt for transformer (TRF) applications, if required\n", "• Type tested to IEC 60840, 62067, and IEEE 48\n", "• Dimensions according to IEC 62271-209 [{'source': 'D:\\\\GENAIProjects\\\\calling-repasentive-ai\\\\calling-agent\\\\data\\\\HighVoltag.pdf'}]\n"]}], "source": ["for doc in results:\n", "    print(f\"* {doc.page_content} [{doc.metadata}]\")"]}, {"cell_type": "code", "execution_count": null, "id": "58910fa6", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(id='46563814-78f7-4a8d-b471-3695de8c5a15', metadata={'source': 'D:\\\\GENAIProjects\\\\calling-repasentive-ai\\\\calling-agent\\\\data\\\\HighVoltag.pdf'}, page_content='35\\nnkt cables SWITCHGEAR & TRANSFORMER TERMINATION Underground Transmission Solutions \\nCable Accessories\\nType Switchgear Transformer KSEV 72  \\nKTEV 72\\nKSEV 145  \\nKTEV 145\\nKSEV 245  \\nKTEV 245\\nOperation Voltage Um (kV) 72 145 245\\nConductor Cu/Al  Max. (kcmil) 2000 2500 5000\\nDiameter Over Dielectric Min.-Max. (inches) 1.5-2.9 1.5-2.9 1.8-3.9\\nDiameter Over Jacket Max. (inches) 3.9 3.9 5.3\\nLength L1 (mm) 310 470 620\\nLength L2 (mm) 582 757 960\\nD1 (mm) 245 266 475\\nD2 (mm) 300 350 500\\nnkt cables \\nSwitchgear & \\nTransformer  \\nTermination \\nKSEV/KTEV DRY TYPE PLUG-IN TERMINATION UP TO 245 kV\\nAll versions of dry-type termination are designed for installation in SF6 gas insulated \\nswitchgear (GIS) or for installation in the oil filled cable box of the transformer. The \\ncomplete termination consists of epoxy resin insulator with embedded electrode, \\nfixing ring which is fitted to the cable, comprising metal cable gland, compression \\ndevice, and premolded plug-in stress cone for electrical field control.')]"]}, "execution_count": 97, "metadata": {}, "output_type": "execute_result"}], "source": ["retriever = dense_vector_store.as_retriever(\n", "    search_type=\"similarity_score_threshold\",\n", "    search_kwargs={\"k\": 1, \"score_threshold\":0.6},\n", ")"]}, {"cell_type": "code", "execution_count": 99, "id": "9e4eeebb", "metadata": {}, "outputs": [], "source": ["from langchain.chains import create_retrieval_chain\n", "from langchain.chains.combine_documents import create_stuff_documents_chain\n", "from langchain_core.prompts import ChatPromptTemplate\n"]}, {"cell_type": "code", "execution_count": 104, "id": "af7fdb33", "metadata": {}, "outputs": [], "source": ["system_prompt = (\n", "    \"You are an Question Answer assistant for question-answering tasks. \"\n", "    \"Use the following pieces of retrieved context to answer \"\n", "    \"the question. If you don't know the answer, say that you \"\n", "    \"don't know. Use three sentences maximum and keep the \"\n", "    \"answer concise.\"\n", "    \"\\n\\n\"\n", "    \"{context}\"\n", ")"]}, {"cell_type": "code", "execution_count": 105, "id": "2dd76c77", "metadata": {}, "outputs": [], "source": ["prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "      (\"system\",system_prompt),\n", "      (\"human\",\"{input}\")\n", "\t ]\n", ")"]}, {"cell_type": "code", "execution_count": 100, "id": "14c84cc7", "metadata": {}, "outputs": [], "source": ["from langchain_groq import ChatGroq"]}, {"cell_type": "code", "execution_count": 46, "id": "1953c29c", "metadata": {}, "outputs": [], "source": ["chatModel = ChatGroq(\n", "     model=\"llama3-8b-8192\",\n", "     api_key=os.getenv(\"GROQ_API_KEY\")\n", ")"]}, {"cell_type": "code", "execution_count": 106, "id": "71469184", "metadata": {}, "outputs": [], "source": ["question_answer_chain = create_stuff_documents_chain(chatModel, prompt)\n", "rag_chain = create_retrieval_chain(retriever, question_answer_chain)"]}, {"cell_type": "code", "execution_count": 108, "id": "876301bc", "metadata": {}, "outputs": [{"data": {"text/plain": ["'A knowledge base is a collection of knowledge that is formally represented and structured to facilitate querying and inference. In the context of natural language processing, a knowledge base can be a database that contains information about entities, relationships, and concepts, which can be used to answer questions or make predictions.'"]}, "execution_count": 108, "metadata": {}, "output_type": "execute_result"}], "source": ["rag_chain.invoke({\"input\":'what is knowlegde base'})['answer']"]}, {"cell_type": "code", "execution_count": null, "id": "8af0503e", "metadata": {}, "outputs": [], "source": ["from concurrent.futures import ThreadPoolExecutor\n", "with ThreadPoolExecutor(max_workers=6) as executor:\n", "        \"\"\"\n", "        Run the queries in parallel.\n", "        \"\"\"\n", "        results = list(executor.map(retriever, query))"]}, {"cell_type": "markdown", "id": "98b05893", "metadata": {}, "source": ["# Optimization"]}, {"cell_type": "code", "execution_count": 77, "id": "14fb0292", "metadata": {}, "outputs": [], "source": ["import os, sys\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain.chains import create_retrieval_chain\n", "from langchain.chains.combine_documents import create_stuff_documents_chain\n", "from langchain.tools.retriever import create_retriever_tool # Expriment\n", "from langchain_groq import ChatGroq\n", "from langchain_pinecone import PineconeVectorStore\n", "from langchain_community.embeddings import HuggingFaceEmbeddings\n", "from src.prompt import system_prompt\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "# sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))\n", "\n", "class SearchDocument:\n", "    def __init__(self) -> None:\n", "        #  Load LLM \n", "        self.llm = ChatGroq(\n", "            model=\"llama3-8b-8192\",\n", "            api_key=os.getenv(\"GROQ_API_KEY\")\n", "        )\n", "\n", "       \n", "        model_kwargs = {'device': 'cpu'}\n", "        encode_kwargs = {'normalize_embeddings': False}\n", "        self.embeddings = HuggingFaceEmbeddings(\n", "            model_name='sentence-transformers/all-MiniLM-L6-v2',\n", "            model_kwargs=model_kwargs,\n", "            encode_kwargs=encode_kwargs\n", "        )\n", "\n", "        #  Load prompt \n", "        self.prompt = ChatPromptTemplate.from_messages([\n", "            (\"system\", system_prompt),\n", "            (\"human\", \"{input}\")\n", "        ])\n", "\n", "        #  Load vector DB and retriever \n", "        self.vector_db = PineconeVectorStore.from_existing_index(\n", "            index_name=\"agentic-rag\",\n", "            embedding=self.embeddings\n", "        )\n", "        self.retriever = self.vector_db.as_retriever()\n", "\n", "        # Create chains \n", "        self.qa_chain = create_stuff_documents_chain(self.llm, self.prompt)\n", "        self.rag_chain = create_retrieval_chain(self.retriever, self.qa_chain)\n", "        \n", "\n", "    def invoke(self,input) -> str:\n", "        try:\n", "            response = self.rag_chain.invoke({\"input\": input})\n", "            return str(response['answer'])\n", "        except Exception as e:\n", "            print(\"Error in Invoke:\", e)\n", "            return \"Error: \" + str(e)\n"]}, {"cell_type": "code", "execution_count": 78, "id": "1731c1a1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Error in Invoke: 'dict' object has no attribute 'replace'\n"]}, {"data": {"text/plain": ["\"Error: 'dict' object has no attribute 'replace'\""]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["SearchDocument().invoke({\"query\": \"types of transformers\"})  # Example usage"]}, {"cell_type": "code", "execution_count": 24, "id": "08fb5f4d", "metadata": {}, "outputs": [], "source": ["import time\n", "def time_stamp(func):\n", "\tdef wrapper():\n", "\t\tstart = time.perf_counter()\n", "\t\tfunc()\n", "\t\tend = time.perf_counter() - start\n", "\t\tprint(end)\n", "\treturn wrapper"]}, {"cell_type": "code", "execution_count": 25, "id": "411d182b", "metadata": {}, "outputs": [], "source": ["from langchain_community.embeddings import HuggingFaceEmbeddings\n", "@time_stamp\n", "def load_embeddings():\n", "    model_kwargs = {'device': 'cpu'}\n", "    encode_kwargs = {'normalize_embeddings': False}\n", "    embeddings = HuggingFaceEmbeddings(\n", "        model_name='sentence-transformers/all-MiniLM-L6-v2',\n", "        model_kwargs=model_kwargs,\n", "        encode_kwargs=encode_kwargs\n", "    )\n", "    return embeddings"]}, {"cell_type": "code", "execution_count": 26, "id": "3b30833b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5.799443500000052\n"]}], "source": ["load_embeddings()"]}, {"cell_type": "code", "execution_count": 27, "id": "e4761126", "metadata": {}, "outputs": [], "source": ["import threading\n", "from functools import wraps\n", "\n", "def run_in_thread(num_threads=1):\n", "\tdef decorator(func):\n", "\t\t@wraps(func)\n", "\t\tdef wrapper(*args, **kwargs):\n", "\t\t\t\tthread = threading.Thread(target=func, args=args, kwargs=kwargs)\n", "\t\t\t\tthread.start()\n", "\t\t\t\tthread.join()\n", "\t\treturn wrapper\n", "\treturn decorator\n"]}, {"cell_type": "code", "execution_count": 30, "id": "704e9658", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5.531463899998926\n"]}], "source": ["@run_in_thread(num_threads=1)\n", "@time_stamp\n", "def load_embeddings():\n", "    model_kwargs = {'device': 'cpu'}\n", "    encode_kwargs = {'normalize_embeddings': False}\n", "    embeddings = HuggingFaceEmbeddings(\n", "        model_name='sentence-transformers/all-MiniLM-L6-v2',\n", "        model_kwargs=model_kwargs,\n", "        encode_kwargs=encode_kwargs\n", "    )\n", "    return embeddings\n", "load_embeddings()"]}, {"cell_type": "markdown", "id": "d7637f2e", "metadata": {}, "source": ["# Agent With Lang<PERSON>in"]}, {"cell_type": "code", "execution_count": 41, "id": "3f854d23", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "if not os.getenv(\"TAVLY_API_KEY\"):\n", "    os.environ[\"TAVILY_API_KEY\"] = getpass.getpass(\"Tavily API key:\\n\")"]}, {"cell_type": "code", "execution_count": null, "id": "9130c0ab", "metadata": {}, "outputs": [], "source": ["from langchain.tools.tavily_search import TavilySearchResults\n", "from langchain.agents import Tool,load_agent,load_tools\n", "from langchain_core.messages import HumanMessage\n", "from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.prebuilt import create_react_agent, ToolNode"]}, {"cell_type": "code", "execution_count": 69, "id": "e2b53a8b", "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel, Field\n", "class TopicSelactionParser(BaseModel):\n", "    Topic : str = Field(description=\"Selected topic\")\n", "    Resoning : str = Field(description=\"Reasoning behind topic selection\")"]}, {"cell_type": "code", "execution_count": 70, "id": "cd1b7fa0", "metadata": {}, "outputs": [], "source": ["from langchain.output_parsers import PydanticOutputParser\n", "perser = PydanticOutputParser(pydantic_object=TopicSelactionParser)"]}, {"cell_type": "code", "execution_count": 71, "id": "344bfa2f", "metadata": {}, "outputs": [], "source": ["from langchain.prompts import PromptTemplate\n", "\n", "def agent(state):\n", "    message = state['messages']\n", "    question = message[-1]\n", "    print(question)\n", "    \n", "    template = \"\"\" \n", "    Your task is to classify the given user query into one of the following categories: [Finance, Not Related]. \n", "    Only respond with the category name and nothing else.\n", "\n", "    User query: {question}\n", "    {format_instructions}\n", "    \"\"\"\n", "    prompt = PromptTemplate(\n", "        template=template,\n", "        input_variables=[\"question\"],\n", "        partial_variables={\n", "            \"format_instructions\": perser.get_format_instructions()\n", "        }\n", "    )\n", "    chain = prompt | chatModel | perser\n", "\n", "    response = chain.invoke({\"question\": question, \"format_instructions\": perser.get_format_instructions()})\n", "\n", "    print(response)\n", "\n", "    return {\"messages\": [response.Topic]}"]}, {"cell_type": "code", "execution_count": 79, "id": "93181d1b", "metadata": {}, "outputs": [], "source": ["def rag(state):\n", "    print(\" -> Calling RAG -->\")\n", "    messages = state['messages']\n", "    question = messages[0] ## Fetching the user question\n", "    print(question)\n", "    rag_chain = SearchDocument()\n", "    response = rag_chain.invoke({\"input\":question})\n", "    return str(response['answer'])"]}, {"cell_type": "code", "execution_count": 80, "id": "c221f46f", "metadata": {}, "outputs": [], "source": ["def llm_call(state):\n", "    print('-> Calling LLM ->')\n", "\n", "    messages = state['messages']\n", "    question = messages[0] ## Fetching the user question\n", "\n", "    # Normal LLM call\n", "    complete_query = \"Anwer the follow question with your knowledge of the real world. Following is the user question: \" + question\n", "    response = chatModel.invoke(complete_query)\n", "    return {\"messages\": [response.content]}"]}, {"cell_type": "code", "execution_count": 81, "id": "0a008e9c", "metadata": {}, "outputs": [], "source": ["def router(state):\n", "    print('-> Router ->')\n", "    \n", "    messages = state[\"messages\"]\n", "    last_message = messages[-1]\n", "    print(last_message)\n", "    if \"I don't know.\" in last_message:\n", "        return 'LL<PERSON> Call'\n", "    else:\n", "        return \"RAG Call\""]}, {"cell_type": "code", "execution_count": 82, "id": "8ab1ac1e", "metadata": {}, "outputs": [], "source": ["from typing import TypedDict, Annotated, Sequence\n", "import operator\n", "from langchain_core.messages import BaseMessage\n", "\n", "class AgentState(TypedDict):\n", "    messages : Annotated[Sequence[BaseMessage],operator.add]"]}, {"cell_type": "code", "execution_count": 83, "id": "c6838f46", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph,END\n", "workflow = StateGraph(AgentState) ### StateGraph with AgentState\n", "\n", "workflow.add_node(\"agent\", agent)\n", "\n", "workflow.add_node(\"RAG\", rag)\n", "\n", "workflow.add_node(\"LLM\", llm_call)\n", "\n", "\n", "workflow.set_entry_point(\"RAG\")\n", "\n", "workflow.set_entry_point(\"agent\")\n", "\n", "workflow.add_conditional_edges(\n", "    \"agent\",\n", "    \n", "    \n", "    router,\n", "    {\n", "        \"RAG Call\": \"RAG\",\n", "        \"LLM Call\": \"LLM\",\n", "    }\n", ")\n", "workflow.add_edge(\"RAG\",END)\n", "\n", "\n", "workflow.add_edge(\"LLM\",END)\n", "\n", "\n", "app=workflow.compile()"]}, {"cell_type": "code", "execution_count": 84, "id": "63c73815", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "try:\n", "    display(Image(app.get_graph().draw_mermaid_png()))\n", "except Exception as e:\n", "    # This requires some extra dependencies and is optional\n", "    print(e)"]}, {"cell_type": "code", "execution_count": 85, "id": "0a17bd0f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" -> Calling RAG -->\n", "Tell me about India's <PERSON>ace Growth\n", "Tell me about India's <PERSON>ace Growth\n", "Topic='Finance' Resoning=\"The user query is about India's finance growth\"\n", "-> Router ->\n", "Finance\n", "Error in Invoke: 'dict' object has no attribute 'replace'\n"]}, {"ename": "TypeError", "evalue": "string indices must be integers, not 'str'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mType<PERSON>rror\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[85]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m inputs = {\u001b[33m\"\u001b[39m\u001b[33mmessages\u001b[39m\u001b[33m\"\u001b[39m: [\u001b[33m\"\u001b[39m\u001b[33mTell me about India\u001b[39m\u001b[33m'\u001b[39m\u001b[33ms Finace Growth\u001b[39m\u001b[33m\"\u001b[39m]}\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m output = \u001b[43mapp\u001b[49m\u001b[43m.\u001b[49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\u001b[43minputs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\site-packages\\langgraph\\pregel\\__init__.py:2719\u001b[39m, in \u001b[36mPregel.invoke\u001b[39m\u001b[34m(self, input, config, stream_mode, output_keys, interrupt_before, interrupt_after, checkpoint_during, debug, **kwargs)\u001b[39m\n\u001b[32m   2716\u001b[39m chunks: \u001b[38;5;28mlist\u001b[39m[Union[\u001b[38;5;28mdict\u001b[39m[\u001b[38;5;28mstr\u001b[39m, Any], Any]] = []\n\u001b[32m   2717\u001b[39m interrupts: \u001b[38;5;28mlist\u001b[39m[Interrupt] = []\n\u001b[32m-> \u001b[39m\u001b[32m2719\u001b[39m \u001b[43m\u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mchunk\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   2720\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m   2721\u001b[39m \u001b[43m    \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2722\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstream_mode\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream_mode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2723\u001b[39m \u001b[43m    \u001b[49m\u001b[43moutput_keys\u001b[49m\u001b[43m=\u001b[49m\u001b[43moutput_keys\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2724\u001b[39m \u001b[43m    \u001b[49m\u001b[43minterrupt_before\u001b[49m\u001b[43m=\u001b[49m\u001b[43minterrupt_before\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2725\u001b[39m \u001b[43m    \u001b[49m\u001b[43minterrupt_after\u001b[49m\u001b[43m=\u001b[49m\u001b[43minterrupt_after\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2726\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcheckpoint_during\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcheckpoint_during\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2727\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdebug\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdebug\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2728\u001b[39m \u001b[43m    \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2729\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m   2730\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mstream_mode\u001b[49m\u001b[43m \u001b[49m\u001b[43m==\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mvalues\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\n\u001b[32m   2731\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   2732\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;28;43misinstance\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mchunk\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mdict\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m   2733\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;129;43;01mand\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43m(\u001b[49m\u001b[43mints\u001b[49m\u001b[43m \u001b[49m\u001b[43m:=\u001b[49m\u001b[43m \u001b[49m\u001b[43mchunk\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[43mINTERRUPT\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mis\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mnot\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\n\u001b[32m   2734\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\site-packages\\langgraph\\pregel\\__init__.py:2436\u001b[39m, in \u001b[36mPregel.stream\u001b[39m\u001b[34m(self, input, config, stream_mode, output_keys, interrupt_before, interrupt_after, checkpoint_during, debug, subgraphs)\u001b[39m\n\u001b[32m   2434\u001b[39m         \u001b[38;5;28;01mfor\u001b[39;00m task \u001b[38;5;129;01min\u001b[39;00m loop.match_cached_writes():\n\u001b[32m   2435\u001b[39m             loop.output_writes(task.id, task.writes, cached=\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[32m-> \u001b[39m\u001b[32m2436\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43m_\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mrunner\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtick\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   2437\u001b[39m \u001b[43m            \u001b[49m\u001b[43m[\u001b[49m\u001b[43mt\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mt\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mloop\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtasks\u001b[49m\u001b[43m.\u001b[49m\u001b[43mvalues\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mnot\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mt\u001b[49m\u001b[43m.\u001b[49m\u001b[43mwrites\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2438\u001b[39m \u001b[43m            \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mstep_timeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2439\u001b[39m \u001b[43m            \u001b[49m\u001b[43mget_waiter\u001b[49m\u001b[43m=\u001b[49m\u001b[43mget_waiter\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2440\u001b[39m \u001b[43m            \u001b[49m\u001b[43mschedule_task\u001b[49m\u001b[43m=\u001b[49m\u001b[43mloop\u001b[49m\u001b[43m.\u001b[49m\u001b[43maccept_push\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2441\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m   2442\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;66;43;03m# emit output\u001b[39;49;00m\n\u001b[32m   2443\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;28;43;01myield from\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43moutput\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   2444\u001b[39m \u001b[38;5;66;03m# emit output\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\site-packages\\langgraph\\pregel\\runner.py:252\u001b[39m, in \u001b[36mPregelRunner.tick\u001b[39m\u001b[34m(self, tasks, reraise, timeout, retry_policy, get_waiter, schedule_task)\u001b[39m\n\u001b[32m    250\u001b[39m \u001b[38;5;66;03m# panic on failure or timeout\u001b[39;00m\n\u001b[32m    251\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m252\u001b[39m     \u001b[43m_panic_or_proceed\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    253\u001b[39m \u001b[43m        \u001b[49m\u001b[43mfutures\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdone\u001b[49m\u001b[43m.\u001b[49m\u001b[43munion\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mt\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mfutures\u001b[49m\u001b[43m.\u001b[49m\u001b[43mitems\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mt\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mis\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mnot\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    254\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpanic\u001b[49m\u001b[43m=\u001b[49m\u001b[43mreraise\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    255\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    256\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[32m    257\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m tb := exc.__traceback__:\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\site-packages\\langgraph\\pregel\\runner.py:509\u001b[39m, in \u001b[36m_panic_or_proceed\u001b[39m\u001b[34m(futs, timeout_exc_cls, panic)\u001b[39m\n\u001b[32m    507\u001b[39m                 interrupts.append(exc)\n\u001b[32m    508\u001b[39m             \u001b[38;5;28;01melif\u001b[39;00m fut \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m SKIP_RERAISE_SET:\n\u001b[32m--> \u001b[39m\u001b[32m509\u001b[39m                 \u001b[38;5;28;01mraise\u001b[39;00m exc\n\u001b[32m    510\u001b[39m \u001b[38;5;66;03m# raise combined interrupts\u001b[39;00m\n\u001b[32m    511\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m interrupts:\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\site-packages\\langgraph\\pregel\\executor.py:80\u001b[39m, in \u001b[36mBackgroundExecutor.done\u001b[39m\u001b[34m(self, task)\u001b[39m\n\u001b[32m     78\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"Remove the task from the tasks dict when it's done.\"\"\"\u001b[39;00m\n\u001b[32m     79\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m80\u001b[39m     \u001b[43mtask\u001b[49m\u001b[43m.\u001b[49m\u001b[43mresult\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     81\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m GraphBubbleUp:\n\u001b[32m     82\u001b[39m     \u001b[38;5;66;03m# This exception is an interruption signal, not an error\u001b[39;00m\n\u001b[32m     83\u001b[39m     \u001b[38;5;66;03m# so we don't want to re-raise it on exit\u001b[39;00m\n\u001b[32m     84\u001b[39m     \u001b[38;5;28mself\u001b[39m.tasks.pop(task)\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\concurrent\\futures\\_base.py:449\u001b[39m, in \u001b[36mFuture.result\u001b[39m\u001b[34m(self, timeout)\u001b[39m\n\u001b[32m    447\u001b[39m     \u001b[38;5;28;01m<PERSON>se\u001b[39;00m CancelledError()\n\u001b[32m    448\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._state == FINISHED:\n\u001b[32m--> \u001b[39m\u001b[32m449\u001b[39m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m__get_result\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    451\u001b[39m \u001b[38;5;28mself\u001b[39m._condition.wait(timeout)\n\u001b[32m    453\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._state \u001b[38;5;129;01min\u001b[39;00m [CANCELLED, CANCELLED_AND_NOTIFIED]:\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\concurrent\\futures\\_base.py:401\u001b[39m, in \u001b[36mFuture.__get_result\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    399\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._exception:\n\u001b[32m    400\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m401\u001b[39m         \u001b[38;5;28;01m<PERSON>se\u001b[39;00m \u001b[38;5;28mself\u001b[39m._exception\n\u001b[32m    402\u001b[39m     \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[32m    403\u001b[39m         \u001b[38;5;66;03m# Break a reference cycle with the exception in self._exception\u001b[39;00m\n\u001b[32m    404\u001b[39m         \u001b[38;5;28mself\u001b[39m = \u001b[38;5;28;01mNone\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\concurrent\\futures\\thread.py:58\u001b[39m, in \u001b[36m_WorkItem.run\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m     55\u001b[39m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m\n\u001b[32m     57\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m58\u001b[39m     result = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     59\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[32m     60\u001b[39m     \u001b[38;5;28mself\u001b[39m.future.set_exception(exc)\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\site-packages\\langgraph\\pregel\\retry.py:40\u001b[39m, in \u001b[36mrun_with_retry\u001b[39m\u001b[34m(task, retry_policy, configurable)\u001b[39m\n\u001b[32m     38\u001b[39m     task.writes.clear()\n\u001b[32m     39\u001b[39m     \u001b[38;5;66;03m# run the task\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m40\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mtask\u001b[49m\u001b[43m.\u001b[49m\u001b[43mproc\u001b[49m\u001b[43m.\u001b[49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtask\u001b[49m\u001b[43m.\u001b[49m\u001b[43minput\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     41\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m ParentCommand \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[32m     42\u001b[39m     ns: \u001b[38;5;28mstr\u001b[39m = config[CONF][CONFIG_KEY_CHECKPOINT_NS]\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\site-packages\\langgraph\\utils\\runnable.py:623\u001b[39m, in \u001b[36mRunnableSeq.invoke\u001b[39m\u001b[34m(self, input, config, **kwargs)\u001b[39m\n\u001b[32m    621\u001b[39m     \u001b[38;5;66;03m# run in context\u001b[39;00m\n\u001b[32m    622\u001b[39m     \u001b[38;5;28;01mwith\u001b[39;00m set_config_context(config, run) \u001b[38;5;28;01mas\u001b[39;00m context:\n\u001b[32m--> \u001b[39m\u001b[32m623\u001b[39m         \u001b[38;5;28minput\u001b[39m = \u001b[43mcontext\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[43mstep\u001b[49m\u001b[43m.\u001b[49m\u001b[43minvoke\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    624\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    625\u001b[39m     \u001b[38;5;28minput\u001b[39m = step.invoke(\u001b[38;5;28minput\u001b[39m, config)\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\site-packages\\langgraph\\utils\\runnable.py:377\u001b[39m, in \u001b[36mRunnableCallable.invoke\u001b[39m\u001b[34m(self, input, config, **kwargs)\u001b[39m\n\u001b[32m    375\u001b[39m         run_manager.on_chain_end(ret)\n\u001b[32m    376\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m377\u001b[39m     ret = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    378\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.recurse \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(ret, Runnable):\n\u001b[32m    379\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m ret.invoke(\u001b[38;5;28minput\u001b[39m, config)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[79]\u001b[39m\u001b[32m, line 8\u001b[39m, in \u001b[36mrag\u001b[39m\u001b[34m(state)\u001b[39m\n\u001b[32m      6\u001b[39m rag_chain = SearchDocument()\n\u001b[32m      7\u001b[39m response = rag_chain.invoke({\u001b[33m\"\u001b[39m\u001b[33minput\u001b[39m\u001b[33m\"\u001b[39m:question})\n\u001b[32m----> \u001b[39m\u001b[32m8\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mstr\u001b[39m(\u001b[43mresponse\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43manswer\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m]\u001b[49m)\n", "\u001b[31mTypeError\u001b[39m: string indices must be integers, not 'str'", "During task with name '<PERSON><PERSON>' and id '57534632-21b4-be26-20f0-69dbdd53e860'"]}], "source": ["inputs = {\"messages\": [\"Tell me about India's Finace Growth\"]}\n", "output = app.invoke(inputs)"]}, {"cell_type": "markdown", "id": "4f6c463e", "metadata": {}, "source": ["# Langgraph"]}, {"cell_type": "code", "execution_count": 2, "id": "6f378ebd", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "import requests\n", "from dotenv import load_dotenv\n", "load_dotenv()"]}, {"cell_type": "markdown", "id": "4ca9db10", "metadata": {}, "source": ["## Custom  Websearch Tool"]}, {"cell_type": "code", "execution_count": 138, "id": "5344796e", "metadata": {}, "outputs": [], "source": ["import os\n", "import requests\n", "\n", "class TaivilySearchTool:\n", "    def __init__(self) -> None:\n", "        self.api_key = os.getenv('TAVLY_API_KEY')\n", "        self.base = \"https://api.tavily.com/search\"\n", "    \n", "    def search(self, query: str, max_results: int = 5) -> str:\n", "        try:\n", "            payload = {\n", "                \"api_key\": self.api_key,\n", "                \"query\": query,\n", "                \"search_depth\": \"advanced\",\n", "                \"include_answer\": True,\n", "                \"include_images\": <PERSON><PERSON><PERSON>,\n", "                \"include_raw_content\": <PERSON>als<PERSON>,\n", "                \"max_results\": max_results\n", "            }\n", "\n", "            response = requests.post(self.base, json=payload, timeout=15)\n", "            response.raise_for_status()\n", "            data = response.json()\n", "            print(data)\n", "            # Extract the top 5 search results from 'content' key\n", "            content_list = data[\"results\"]\n", "            print(content_list)\n", "            \n", "\n", "            return content_list\n", "\n", "        except Exception as e:\n", "            return f\"Error during search: {str(e)}\"\n"]}, {"cell_type": "code", "execution_count": 139, "id": "3129bbcd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'query': 'What is the capital of France?', 'follow_up_questions': None, 'answer': 'The capital of France is Paris. It is the largest city and cultural center of France. Paris has a rich history and is known globally for its art and fashion.', 'images': [], 'results': [{'url': 'https://en.wikipedia.org/wiki/Paris', 'title': 'Paris - Wikipedia', 'content': 'Paris is the capital and largest city of France. With an estimated population of 2,048,472 in January 2025 in an area of more than 105 km2 (41 sq mi),', 'score': 0.98567, 'raw_content': None}, {'url': 'https://www.coe.int/en/web/interculturalcities/paris', 'title': 'Paris, France - Intercultural City - The Council of Europe', 'content': 'Paris, France - Intercultural City - Intercultural Cities Programme Council of Europe Human Rights Human Rights Online resources Human Rights Online resources # Intercultural Cities Programme - What is an intercultural city? - Intercultural Regions - Index reports per city - Intercultural profiles 2. Intercultural Cities Programme 4. Paris # Paris, France - Intercultural City Paris is the capital and most populous city of France. The City of Paris has an area of 105\\xa0km² and a population of 2,241,346 (2014 estimate). #### International Intercultural Cities Network - Paris Intercultural profile Intercultural Cities Index - Paris\\xa0city official website - Paris Facebook page - Private office of the Secretary General - Online bookshop - Online resources **Council of Europe**,', 'score': 0.98283, 'raw_content': None}, {'url': 'https://en.wikipedia.org/wiki/France', 'title': 'France - Wikipedia', 'content': \"In the High Middle Ages, France was a powerful but decentralized feudal kingdom, but from the mid-14th to the mid-15th centuries, France was plunged into a dynastic conflict with England known as the Hundred Years' War. In the 16th century, French culture flourished during the French Renaissance and a French colonial empire emerged. France reached its political and military zenith in the early 19th century under Napoleon Bonaparte, subjugating part of continental Europe and establishing the First French Empire. Since the 1995 public transport bombings, France has been targeted by Islamist organisations, notably the Charlie Hebdo attack in 2015 which provoked the largest public rallies in French history, gathering 4.4 million people, the November 2015 Paris attacks which resulted in 130 deaths, the deadliest attack on French soil since World War II and the deadliest in the European Union since the Madrid train bombings in 2004.\", 'score': 0.98112, 'raw_content': None}, {'url': 'https://home.adelphi.edu/~ca19535/page%204.html', 'title': 'Paris facts: the capital of France in history', 'content': 'Paris, France   ## Paris facts: Paris, the **capital of France** Paris is the **capital of France**, Paris has 2.234 million inhabitants ## Paris facts: Paris history Republic, Paris has a rich 2000 year history. See details of Paris churches, including Notre ## Paris facts: Paris, a world city Paris is a world capital city of shopping french fashion brands. All of this turns Paris into a ## Paris facts: the capital of France in history Before Paris, the capital of France Paris first became the capital of France in France, Paris retrieved its status of capital of France under King can see remains of the Philippe August Paris walls in the parking and Louvre Museum Paris remained the capital of', 'score': 0.97782, 'raw_content': None}, {'url': 'https://www.britannica.com/place/Paris', 'title': 'Paris | Definition, Map, Population, Facts, & History | Britannica', 'content': 'Paris # Paris **Paris**, city and capital of France, situated in the north-central part of the country. Paris Paris(more) For centuries Paris has been one of the world’s most important and attractive cities. Paris, France Paris, Paris, Paris The Promenade Plantée is a partially elevated parkway built along an abandoned rail line and viaduct in the 12th *arrondissement* (municipal district) of Paris, on the right bank of the Seine River. Over the centuries, as Paris expanded outward from the Île de la Cité, various walls were built to enclose parts of the city. Situated in the Seine in the centre of Paris, the ship-shaped Île de la Cité is the historical heart of the city. \"Paris\".', 'score': 0.97611, 'raw_content': None}], 'response_time': 1.9}\n", "[{'url': 'https://en.wikipedia.org/wiki/Paris', 'title': 'Paris - Wikipedia', 'content': 'Paris is the capital and largest city of France. With an estimated population of 2,048,472 in January 2025 in an area of more than 105 km2 (41 sq mi),', 'score': 0.98567, 'raw_content': None}, {'url': 'https://www.coe.int/en/web/interculturalcities/paris', 'title': 'Paris, France - Intercultural City - The Council of Europe', 'content': 'Paris, France - Intercultural City - Intercultural Cities Programme Council of Europe Human Rights Human Rights Online resources Human Rights Online resources # Intercultural Cities Programme - What is an intercultural city? - Intercultural Regions - Index reports per city - Intercultural profiles 2. Intercultural Cities Programme 4. Paris # Paris, France - Intercultural City Paris is the capital and most populous city of France. The City of Paris has an area of 105\\xa0km² and a population of 2,241,346 (2014 estimate). #### International Intercultural Cities Network - Paris Intercultural profile Intercultural Cities Index - Paris\\xa0city official website - Paris Facebook page - Private office of the Secretary General - Online bookshop - Online resources **Council of Europe**,', 'score': 0.98283, 'raw_content': None}, {'url': 'https://en.wikipedia.org/wiki/France', 'title': 'France - Wikipedia', 'content': \"In the High Middle Ages, France was a powerful but decentralized feudal kingdom, but from the mid-14th to the mid-15th centuries, France was plunged into a dynastic conflict with England known as the Hundred Years' War. In the 16th century, French culture flourished during the French Renaissance and a French colonial empire emerged. France reached its political and military zenith in the early 19th century under Napoleon Bonaparte, subjugating part of continental Europe and establishing the First French Empire. Since the 1995 public transport bombings, France has been targeted by Islamist organisations, notably the Charlie Hebdo attack in 2015 which provoked the largest public rallies in French history, gathering 4.4 million people, the November 2015 Paris attacks which resulted in 130 deaths, the deadliest attack on French soil since World War II and the deadliest in the European Union since the Madrid train bombings in 2004.\", 'score': 0.98112, 'raw_content': None}, {'url': 'https://home.adelphi.edu/~ca19535/page%204.html', 'title': 'Paris facts: the capital of France in history', 'content': 'Paris, France   ## Paris facts: Paris, the **capital of France** Paris is the **capital of France**, Paris has 2.234 million inhabitants ## Paris facts: Paris history Republic, Paris has a rich 2000 year history. See details of Paris churches, including Notre ## Paris facts: Paris, a world city Paris is a world capital city of shopping french fashion brands. All of this turns Paris into a ## Paris facts: the capital of France in history Before Paris, the capital of France Paris first became the capital of France in France, Paris retrieved its status of capital of France under King can see remains of the Philippe August Paris walls in the parking and Louvre Museum Paris remained the capital of', 'score': 0.97782, 'raw_content': None}, {'url': 'https://www.britannica.com/place/Paris', 'title': 'Paris | Definition, Map, Population, Facts, & History | Britannica', 'content': 'Paris # Paris **Paris**, city and capital of France, situated in the north-central part of the country. Paris Paris(more) For centuries Paris has been one of the world’s most important and attractive cities. Paris, France Paris, Paris, Paris The Promenade Plantée is a partially elevated parkway built along an abandoned rail line and viaduct in the 12th *arrondissement* (municipal district) of Paris, on the right bank of the Seine River. Over the centuries, as Paris expanded outward from the Île de la Cité, various walls were built to enclose parts of the city. Situated in the Seine in the centre of Paris, the ship-shaped Île de la Cité is the historical heart of the city. \"Paris\".', 'score': 0.97611, 'raw_content': None}]\n"]}], "source": ["l =TaivilySearchTool().search(\"What is the capital of France?\")  # Example usage"]}, {"cell_type": "code", "execution_count": 140, "id": "f401032b", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Paris is the capital and largest city of France. With an estimated population of 2,048,472 in January 2025 in an area of more than 105 km2 (41 sq mi),\\nParis, France - Intercultural City - Intercultural Cities Programme Council of Europe Human Rights Human Rights Online resources Human Rights Online resources # Intercultural Cities Programme - What is an intercultural city? - Intercultural Regions - Index reports per city - Intercultural profiles 2. Intercultural Cities Programme 4. Paris # Paris, France - Intercultural City Paris is the capital and most populous city of France. The City of Paris has an area of 105\\xa0km² and a population of 2,241,346 (2014 estimate). #### International Intercultural Cities Network - Paris Intercultural profile Intercultural Cities Index - Paris\\xa0city official website - Paris Facebook page - Private office of the Secretary General - Online bookshop - Online resources **Council of Europe**,\\nIn the High Middle Ages, France was a powerful but decentralized feudal kingdom, but from the mid-14th to the mid-15th centuries, France was plunged into a dynastic conflict with England known as the Hundred Years\\' War. In the 16th century, French culture flourished during the French Renaissance and a French colonial empire emerged. France reached its political and military zenith in the early 19th century under <PERSON>, subjugating part of continental Europe and establishing the First French Empire. Since the 1995 public transport bombings, France has been targeted by Islamist organisations, notably the Charlie He<PERSON>do attack in 2015 which provoked the largest public rallies in French history, gathering 4.4 million people, the November 2015 Paris attacks which resulted in 130 deaths, the deadliest attack on French soil since World War II and the deadliest in the European Union since the Madrid train bombings in 2004.\\nParis, France   ## Paris facts: Paris, the **capital of France** Paris is the **capital of France**, Paris has 2.234 million inhabitants ## Paris facts: Paris history Republic, Paris has a rich 2000 year history. See details of Paris churches, including Notre ## Paris facts: Paris, a world city Paris is a world capital city of shopping french fashion brands. All of this turns Paris into a ## Paris facts: the capital of France in history Before Paris, the capital of France Paris first became the capital of France in France, Paris retrieved its status of capital of France under King can see remains of the Philippe August Paris walls in the parking and Louvre Museum Paris remained the capital of\\nParis # Paris **Paris**, city and capital of France, situated in the north-central part of the country. Paris Paris(more) For centuries Paris has been one of the world’s most important and attractive cities. Paris, France Paris, Paris, Paris The Promenade Plantée is a partially elevated parkway built along an abandoned rail line and viaduct in the 12th *arrondissement* (municipal district) of Paris, on the right bank of the Seine River. Over the centuries, as Paris expanded outward from the Île de la Cité, various walls were built to enclose parts of the city. Situated in the Seine in the centre of Paris, the ship-shaped Île de la Cité is the historical heart of the city. \"Paris\".'"]}, "execution_count": 140, "metadata": {}, "output_type": "execute_result"}], "source": ["\"\\n\".join([i['content'] for i in l])"]}, {"cell_type": "code", "execution_count": 6, "id": "ff80a6d1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading components...\n", "  Loading LLM...\n", "  Loading vector database...\n", "Components loaded!\n", "  Loading LLM...\n"]}], "source": ["from src.rag.retrieval import SearchDocument\n", "\n", "obj= SearchDocument()\n", "llm= obj._load_llm()"]}, {"cell_type": "code", "execution_count": 1, "id": "968874a7", "metadata": {}, "outputs": [], "source": ["from concurrent.futures import ThreadPoolExecutor"]}, {"cell_type": "code", "execution_count": 2, "id": "b274000c", "metadata": {}, "outputs": [], "source": ["from langchain.agents import Tool\n", "from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.prebuilt import create_react_agent, ToolNode"]}, {"cell_type": "code", "execution_count": 13, "id": "fad198b1", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"Hello World! It's great to meet you! Is there something I can help you with or would you like to chat?\""]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["llm.invoke(\"Hello World\").content"]}, {"cell_type": "markdown", "id": "17ee176f", "metadata": {}, "source": ["# Langchain Tools"]}, {"cell_type": "code", "execution_count": 80, "id": "9e0b1329", "metadata": {}, "outputs": [], "source": ["obj = SearchDocument()\n", "response = obj.invoke( \"types of transformers\")  # Example usage"]}, {"cell_type": "code", "execution_count": 82, "id": "a328e8e0", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'str' object has no attribute 'binary_score'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36m<PERSON><PERSON>\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[82]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[43mresponse\u001b[49m\u001b[43m.\u001b[49m\u001b[43mbinary_score\u001b[49m\n", "\u001b[31mAttributeError\u001b[39m: 'str' object has no attribute 'binary_score'"]}], "source": ["response.binary_score"]}, {"cell_type": "code", "execution_count": 3, "id": "de8bab22", "metadata": {}, "outputs": [], "source": ["from typing import List, TypedDict\n", "from langchain.schema import Document\n", "\n", "class GraphState(TypedDict):\n", "    \"\"\"\n", "    Represents a state of a graph.\n", "\n", "    Attributes:\n", "        question: Question\n", "        generation: LLM Generation\n", "        use_web_search: wether to use web search\n", "        documents: List of documents\n", "    \"\"\"\n", "\n", "    question: str\n", "    generation: str\n", "    use_web_search: bool\n", "    documents: List[Document]"]}, {"cell_type": "markdown", "id": "e2d59fbc", "metadata": {}, "source": ["## Web search"]}, {"cell_type": "code", "execution_count": null, "id": "af246ecb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 127, "id": "13ef06d1", "metadata": {}, "outputs": [], "source": ["from typing import List, Dict, Any\n", "from langchain.schema import Document\n", "import os\n", "import requests\n", "from dotenv import  load_dotenv\n", "load_dotenv()\n", "\n", "# Web Search Tool\n", "class TaivilySearchTool:\n", "    def __init__(self) -> None:\n", "        self.api_key = os.getenv('TAVLY_API_KEY')\n", "        self.base_url = \"https://api.tavily.com/search\"\n", "    \n", "    def search(self, query: str, max_results: int = 5) -> List[Dict]:\n", "        try:\n", "            payload = {\n", "                \"api_key\": self.api_key,\n", "                \"query\": query,\n", "                \"search_depth\": \"advanced\",\n", "                \"include_answer\": True,\n", "                \"include_images\": <PERSON><PERSON><PERSON>,\n", "                \"include_raw_content\": <PERSON>als<PERSON>,\n", "                \"max_results\": max_results\n", "            }\n", "\n", "            response = requests.post(self.base_url, json=payload, timeout=15)\n", "            response.raise_for_status()\n", "            data = response.json()\n", "            return data.get(\"results\", [])\n", "\n", "        except Exception as e:\n", "            print(f\"Error during search: {str(e)}\")\n", "            return []\n", "        \n", "    def web_search(self, state: GraphState) -> Dict[str, Any]:\n", "        \"\"\"Web search tool to fetch information from the web.\"\"\"\n", "        print(\"=\"*20, 'Web Search Tool Invoked', \"=\"*20)\n", "        question = state['question']\n", "        documents = state.get('documents', [])\n", "        \n", "        web_search_results = self.search(question)\n", "        \n", "        if web_search_results:\n", "            # Join all search results into one document\n", "            if type(web_search_results) == str:\n", "                web_search_results\n", "            else :\n", "                  joined_results = \"\\n\".join([result.get('content', '') for result in web_search_results])\n", "                  search_doc = Document(page_content=joined_results)\n", "                  documents.append(search_doc)\n", "        \n", "        print(\"=\"*20, 'Web Search Tool Completed', \"=\"*20)\n", "        return {\"documents\": documents, \"question\": question}"]}, {"cell_type": "code", "execution_count": 8, "id": "47db676a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================== Web Search Tool Invoked ====================\n", "==================== Web Search Tool Completed ====================\n", "{'documents': [Document(metadata={}, page_content='Computing is any goal-oriented activity requiring, benefiting from, or creating computing machinery. It includes the study and experimentation of algorithmic\\nThe history of computing is longer than the history of computing hardware and modern computing technology and includes the history of methods intended for pen\\nTuring is later involved in the development of the Turing-Welchman Bombe, an electro-mechanical device designed to decipher Nazi codes during World War II, according to the UK\\'s National Museum of Computing. 1941: German inventor and engineer <PERSON> completes his Z3 machine, the world\\'s earliest digital computer, according to <PERSON>\\'<PERSON>\\'s book \"A Brief History of Computing\" (Springer, 2021). <PERSON>'s Difference Engine, designed in the 1820s, is considered the first \"mechanical\" computer in history, according to the Science Museum in the U.K. Powered by steam with a hand crank, the machine calculated a series of values and printed the results in a table.\\nThe Modern History of Computing (Stanford Encyclopedia of Philosophy) Stanford Encyclopedia of Philosophy Menu Browse Table of Contents What\\'s New Random Entry Chronological Archives About Editorial Information About the SEP Editorial Board How to Cite the SEP Special Characters Advanced Tools Contact Support SEP Support the SEP PDFs for SEP Friends Make a Donation SEPIA for Libraries Entry Navigation Entry Contents Bibliography Academic Tools Friends PDF Preview Author and Citation Info Back to Top The Modern History of Computing First published Mon Dec 18, 2000; substantive revision Fri Jun 9, 2006 Historically, computers were human clerks who calculated in accordance with effective methods. The term computing machine, used increasingly from the 1920s, refers to any machine that does the work of a human computer, i.e., any machine that calculates in accordance with effective methods. This entry surveys the history of these machines. Babbage\\'s proposed Difference Engine was a special-purpose digital computing machine for the automatic production of mathematical tables (such as logarithm tables, tide tables, and astronomical tables).\\nThe book\\'s definition of computing system is \"All basic hardware and software that work together to run program.\" At the end of the chapter, the book asked the question: \"What is a computing system and provide examples of computing systems.\" I wrote CPU, GPU, RAMS and memory cards as examples of computing systems, but the answer from the book said, \"A computing system is any kind of computing devices such as laptops, phones, and tablets.\" But when I searched examples of computing systems on Google, there were keyboards, barcode scanner, and touchscreen and I don\\'t think these are examples of computing devices which is not right to the definition from the book (isn\\'t keyboard just input device (hardware)?) *   About Reddit')], 'question': 'What is  computing?'}\n"]}], "source": ["state = {\n", "    \"question\": \"What is  computing?\",\n", "    \"documents\": []\n", "}\n", "\n", "search_tool = TaivilySearchTool()\n", "updated_state = search_tool.web_search(state)\n", "\n", "print(updated_state)\n"]}, {"cell_type": "markdown", "id": "b4010698", "metadata": {}, "source": ["## Generate"]}, {"cell_type": "code", "execution_count": 9, "id": "9e03e07f", "metadata": {}, "outputs": [], "source": ["genearation_prompt = \"\"\"\n", "You are a helpful assistant. Use the provided context to answer the user question.\n", "\n", "<context>\n", "{context}\n", "</context>\n", "\n", "Question: {question}\n", "\n", "Answer in a concise and informative manner.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 13, "id": "f58cf0a4", "metadata": {}, "outputs": [], "source": ["llm = ChatGroq(\n", "     model=\"llama3-8b-8192\",\n", "     api_key=os.getenv(\"GROQ_API_KEY\")\n", ")"]}, {"cell_type": "code", "execution_count": 114, "id": "7a3b412f", "metadata": {}, "outputs": [], "source": ["import os\n", "import threading\n", "from concurrent.futures import ThreadPoolExecutor\n", "from typing import Dict, Any\n", "#from src.prompt import genearation_prompt # <- this is a string\n", "from langchain_groq import ChatGroq\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "\n", "# Generation Tool\n", "class GenerationTool:\n", "    def __init__(self,llm) -> None:\n", "        self.lock = threading.Lock()\n", "        self.llm = llm\n", "        print(\"Generation component loaded!\")\n", "\n", "    def generate(self, state: GraphState) -> Dict[str, Any]:\n", "        print(\"=\" * 20, 'Generation Tool Invoked', \"=\" * 20)\n", "        question = state['question']\n", "        documents = state['documents']\n", "\n", "        # Format documents for context\n", "        context = \"\\n\\n\".join([doc for doc in documents])\n", "        \n", "        prompt_template = ChatPromptTemplate.from_template(genearation_prompt)\n", "        generation_chain = prompt_template | self.llm | StrOutputParser()\n", "        \n", "        output = generation_chain.invoke({\"context\": context, \"question\": question})\n", "        \n", "        print(\"=\" * 20, 'Generation Completed', \"=\" * 20)\n", "        return {\"generation\": output, \"documents\": documents, \"question\": question}\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generation component loaded!\n", "==================== Generation Tool Invoked ====================\n", "==================== Generation Completed ====================\n", "\n", "Final Output:\n", " {'generation': 'According to the provided context, computing is any goal-oriented activity requiring, benefiting from, or creating computing machinery. It also includes the study and experimentation of algorithmic methods.', 'documents': [Document(metadata={}, page_content='Computing is any goal-oriented activity requiring, benefiting from, or creating computing machinery. It includes the study and experimentation of algorithmic\\nThe history of computing is longer than the history of computing hardware and modern computing technology and includes the history of methods intended for pen\\nTuring is later involved in the development of the Turing-Welchman Bombe, an electro-mechanical device designed to decipher Nazi codes during World War II, according to the UK\\'s National Museum of Computing. 1941: German inventor and engineer <PERSON> completes his Z3 machine, the world\\'s earliest digital computer, according to <PERSON>\\'<PERSON>\\'s book \"A Brief History of Computing\" (Springer, 2021). <PERSON>\\'s Difference Engine, designed in the 1820s, is considered the first \"mechanical\" computer in history, according to the Science Museum in the U.K. Powered by steam with a hand crank, the machine calculated a series of values and printed the results in a table.\\nThe Modern History of Computing (Stanford Encyclopedia of Philosophy) Stanford Encyclopedia of Philosophy Menu <PERSON>rowse Table of Contents What\\'s New Random Entry Chronological Archives About Editorial Information About the SEP Editorial Board How to Cite the SEP Special Characters Advanced Tools Contact Support SEP Support the SEP PDFs for SEP Friends Make a Donation SEPIA for Libraries Entry Navigation Entry Contents Bibliography Academic Tools Friends PDF Preview Author and Citation Info Back to Top The Modern History of Computing First published Mon Dec 18, 2000; substantive revision Fri Jun 9, 2006 Historically, computers were human clerks who calculated in accordance with effective methods. The term computing machine, used increasingly from the 1920s, refers to any machine that does the work of a human computer, i.e., any machine that calculates in accordance with effective methods. This entry surveys the history of these machines. Babbage\\'s proposed Difference Engine was a special-purpose digital computing machine for the automatic production of mathematical tables (such as logarithm tables, tide tables, and astronomical tables).\\nThe book\\'s definition of computing system is \"All basic hardware and software that work together to run program.\" At the end of the chapter, the book asked the question: \"What is a computing system and provide examples of computing systems.\" I wrote CPU, GPU, RAMS and memory cards as examples of computing systems, but the answer from the book said, \"A computing system is any kind of computing devices such as laptops, phones, and tablets.\" But when I searched examples of computing systems on Google, there were keyboards, barcode scanner, and touchscreen and I don\\'t think these are examples of computing devices which is not right to the definition from the book (isn\\'t keyboard just input device (hardware)?) *   About Reddit')], 'question': 'What is  computing?'}\n"]}], "source": ["state = {\n", "        \"question\": \"What is <PERSON><PERSON><PERSON><PERSON>?\",\n", "        \"documents\": \"LangChain is a framework to build LLM applications using composable modules.\"\n", "    }\n", "\n", "updated_state = GenerationTool(llm=llm).generate(updated_state)\n", "print(\"\\nFinal Output:\\n\", updated_state)"]}, {"cell_type": "markdown", "id": "bd7d0888", "metadata": {}, "source": ["## Retriver"]}, {"cell_type": "code", "execution_count": 3, "id": "d3a7ffee", "metadata": {}, "outputs": [], "source": ["from typing import Any, Dict\n", "from src.rag.retrieval import SearchDocument"]}, {"cell_type": "code", "execution_count": 80, "id": "65c9cbbb", "metadata": {}, "outputs": [], "source": ["# Modify\n", "import os, sys\n", "from functools import lru_cache\n", "from concurrent.futures import ThreadPoolExecutor\n", "import threading\n", "from typing import List\n", "from langchain_groq import ChatGroq\n", "from langchain.chains import create_retrieval_chain\n", "from langchain.chains.combine_documents import create_stuff_documents_chain\n", "from langchain_pinecone import PineconeVectorStore\n", "from langchain_community.embeddings import HuggingFaceEmbeddings\n", "from src.prompt import system_prompt\n", "\n", "class SearchDocument:\n", "    def __init__(self,llm, max_workers: int = 6) -> None:\n", "        self.max_workers = max_workers\n", "        self.llm = llm\n", "        \n", "        # Simple thread lock for safety\n", "        self.lock = threading.Lock()\n", "        \n", "        # Load components in parallel (faster startup)\n", "        self._parallel_init()\n", "        \n", "\t\t  # Load retriever\n", "        self.retriever=self._load_retriever()\n", "        \n", "        # Create the RAG chain\n", "        self._create_chain()\n", "    \n", "    def _parallel_init(self):\n", "        \"\"\"Load LLM and retriever at the same time\"\"\"\n", "        print(\"Loading components...\")\n", "        \n", "        with ThreadPoolExecutor(max_workers=4) as executor:\n", "            # Start loading  components simultaneously\n", "            retriever_future = executor.submit(self._load_retriever)\n", "            \n", "            # Wait for  complete\n", "            self.retriever = retriever_future.result()\n", "            \n", "        print(\"Components loaded!\")\n", "        \n", "    \n", "    def _load_retriever(self):\n", "        \"\"\"Load the vector database\"\"\"\n", "        print(\"  Loading vector database...\")\n", "        \n", "        embeddings = HuggingFaceEmbeddings(\n", "            model_name='sentence-transformers/all-MiniLM-L6-v2',\n", "            model_kwargs={'device': 'cpu'},\n", "            encode_kwargs={'normalize_embeddings': False}\n", "        )\n", "\n", "        vector_db = PineconeVectorStore.from_existing_index(\n", "            index_name=\"agentic-rag\",\n", "            embedding=embeddings\n", "        )\n", "        \n", "        return vector_db.as_retriever(\n", "            search_type=\"similarity\",\n", "            search_kwargs={'k': 5}\n", "        )\n", "    \n", "    def _create_chain(self):\n", "        \"\"\"Create the RAG chain\"\"\"\n", "        self.prompt = ChatPromptTemplate.from_messages([\n", "            (\"system\", system_prompt),\n", "            (\"human\", \"{input}\")\n", "        ])\n", "        \n", "        qa_chain = create_stuff_documents_chain(self.llm, self.prompt)\n", "        self.rag_chain = create_retrieval_chain(self.retriever, qa_chain)\n", "\n", "\n", "    @lru_cache(maxsize=90)\n", "    def invoke(self, input_text: str) -> str:\n", "        \"\"\"Process a single query\"\"\"\n", "        try:\n", "            with self.lock:  # Thread safety\n", "                response = self.rag_chain.invoke({\"input\": input_text})\n", "                return str(response['answer'])\n", "        except Exception as e:\n", "            return f\"Error: {str(e)}\"\n", "        \n", "    @lru_cache(maxsize=50)\n", "    def batch_invoke(self, inputs: List[str]) -> List[str]:\n", "        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:\n", "            futures = [executor.submit(self.invoke, inp) for inp in inputs]\n", "            return [f.result() for f in futures]\n", "\n"]}, {"cell_type": "code", "execution_count": 37, "id": "73e4e77a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading components...\n", "  Loading vector database...\n", "Components loaded!\n", "  Loading vector database...\n"]}], "source": ["llm =ChatGroq(model=\"llama3-8b-8192\",\n", "                api_key=os.getenv(\"GROQ_API_KEY\")\n", "            )\n", "obj = SearchDocument(llm=llm)"]}, {"cell_type": "code", "execution_count": 38, "id": "af0f08e7", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"I don't know. The provided context does not mention specific types of transformers, but rather refers to a termination system for gas-insulated switchgear and transformers.\""]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["obj.invoke(\"types of transformer\")"]}, {"cell_type": "code", "execution_count": 51, "id": "75a4a964", "metadata": {}, "outputs": [], "source": ["doc=obj.retriever.invoke(\" switchgear and transformers\")"]}, {"cell_type": "code", "execution_count": 108, "id": "db5587ff", "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "class RetrieverTool:\n", "    def __init__(self,llm):\n", "        self.llm = llm\n", "        self.search_doc = SearchDocument(llm=llm)\n", "        print(\"=\" * 20, ' Retriever Tool Invoked', \"=\" * 20)\n", "\n", "    def search(self, state: GraphState):\n", "        \"\"\"High-performance document retrieval with caching\"\"\"\n", "\n", "        start_time = time.time()\n", "        question = state['question']\n", "        print(question)\n", "\n", "        try:\n", "            print(f\"🔍 Retrieving documents for: '{question}...'\")\n", "\n", "            documents = self.search_doc.invoke(question)\n", "\n", "            print(f\"Rag Tool Response: {documents}\")\n", "            print(f\"Time Take: {time.time() - start_time}\")\n", "            \n", "            print(\"=\" * 20, ' Retriever Tool Invoked Completed', \"=\" * 20)\n", "            return {\"documents\": documents, \"question\": question}\n", "\n", "        except Exception as e:\n", "            print(f\"Vectore Scarch Error {e}\")\n", "            raise e"]}, {"cell_type": "code", "execution_count": 109, "id": "3b71a668", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading components...\n", "  Loading vector database...\n", "Components loaded!\n", "  Loading vector database...\n", "====================  Retriever Tool Invoked ====================\n", " switchgear and transformers\n", "🔍 Retrieving documents for: ' switchgear and transformers...'\n", "Rag Tool Response: The retrieved context discusses ABB Switchgear & Transformer Termination and nkt cables Switchgear & Transformer Termination.\n", "Time Take: 6.120862722396851\n", "====================  Retriever Tool Invoked Completed ====================\n"]}, {"data": {"text/plain": ["{'documents': 'The retrieved context discusses ABB Switchgear & Transformer Termination and nkt cables Switchgear & Transformer Termination.',\n", " 'question': ' switchgear and transformers'}"]}, "execution_count": 109, "metadata": {}, "output_type": "execute_result"}], "source": ["r = RetrieverTool(llm=llm)\n", "state = {\n", "        \"question\": \" switchgear and transformers\",\n", "    }\n", "r.search(state)"]}, {"cell_type": "markdown", "id": "d6a77b0c", "metadata": {}, "source": ["## Retrieve document Validation"]}, {"cell_type": "code", "execution_count": 20, "id": "2d794c62", "metadata": {}, "outputs": [], "source": ["grade_prompt = \"\"\"You are a grader assessing relevance of a retrieved document to a user question. \\n \n", "    If the document contains keyword(s) or semantic meaning related to the question, grade it as relevant. \\n\n", "    Give a binary score 'yes' or 'no' score to indicate whether the document is relevant to the question.\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "1681c278", "metadata": {}, "outputs": [], "source": ["# from typing import Any, Dict, List\n", "# from langchain_core.pydantic_v1 import BaseModel, Field\n", "# from langchain_core.prompts import ChatPromptTemplate\n", "\n", "# # Document Validation Tool\n", "# class ValidateDocument(BaseModel):\n", "#     \"\"\"Binary score for the relevance check of retrieved documents\"\"\"\n", "#     binary_score: str = Field(\n", "#         description=\"Documents are relevant to the question? 'yes' or 'no'\"\n", "#     )\n", "\n", "# class DocumentValidator:\n", "#     def __init__(self, llm):\n", "#         self.llm = llm\n", "#         self.structured_output = ValidateDocument\n", "\n", "#     def validation_chain(self, question: str, document: str):\n", "#         structured_llm = self.llm.with_structured_output(self.structured_output)\n", "        \n", "#         prompt = ChatPromptTemplate.from_messages([\n", "#             (\"system\", grade_prompt),\n", "#             (\"human\", \"Retrieved document: \\n\\n {document} \\n\\n User question: {question}\")\n", "#         ])\n", "\n", "#         chain = prompt | structured_llm\n", "#         response = chain.invoke({\"question\": question, \"document\": document})\n", "#         return response\n", "\n", "#     def validate_documents(self, state: GraphState) -> Dict[str, Any]:\n", "#         print(\"=\" * 20, 'Document Validation Tool Invoked', \"=\" * 20)\n", "#         question = state[\"question\"]\n", "#         documents = state['documents']\n", "\n", "#         filtered_docs = []\n", "#         use_web_search = False\n", "\n", "#         for doc in documents:\n", "#             result = self.validation_chain(question, doc.page_content)\n", "#             grade = result.binary_score\n", "#             if grade.lower() == \"yes\":\n", "#                 print(\"---------- Relevant Document -----------\")\n", "#                 filtered_docs.append(doc)\n", "#             else:\n", "#                 print(\"---------- Not Relevant Document -----------\")\n", "#                 use_web_search = True\n", "\n", "#         print(\"=\" * 20, 'Document Validation Completed', \"=\" * 20)\n", "#         return {\n", "#             \"documents\": filtered_docs,\n", "#             \"use_web_search\": use_web_search,\n", "#             \"question\": question,\n", "#         }\n"]}, {"cell_type": "code", "execution_count": 131, "id": "ac4753bb", "metadata": {}, "outputs": [], "source": ["from typing import Any, Dict, List\n", "from langchain_core.pydantic_v1 import BaseModel, Field\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "# Document Validation Tool\n", "class ValidateDocument(BaseModel):\n", "    \"\"\"Binary score for the relevance check of retrieved documents\"\"\"\n", "    binary_score: str = Field(\n", "        description=\"Documents are relevant to the question? 'yes' or 'no'\"\n", "    )\n", "\n", "class DocumentValidatorv1:\n", "    def __init__(self, llm):\n", "        self.llm = llm\n", "        self.structured_output = ValidateDocument\n", "\n", "    def validation_chain(self, question: str, document: str):\n", "        structured_llm = self.llm.with_structured_output(self.structured_output)\n", "        \n", "        prompt = ChatPromptTemplate.from_messages([\n", "            (\"system\", grade_prompt),\n", "            (\"human\", \"Retrieved document: \\n\\n {document} \\n\\n User question: {question}\")\n", "        ])\n", "\n", "        chain = prompt | structured_llm\n", "        response = chain.invoke({\"question\": question, \"document\": document})\n", "        return response\n", "\n", "    def validate_documents(self, state: GraphState) -> Dict[str, Any]:\n", "        print(\"=\" * 20, 'Document Validation Tool Invoked', \"=\" * 20)\n", "        question = state[\"question\"]\n", "        documents = state['documents']\n", "\n", "        filtered_docs = []\n", "        use_web_search = False\n", "        result = self.validation_chain(question, documents)\n", "        grade = result.binary_score\n", "        if grade.lower() == \"yes\":\n", "            print(\"---------- Relevant Document -----------\")\n", "            filtered_docs.append(result)\n", "            print(filtered_docs)\n", "        else:\n", "            print(\"---------- Not Relevant Document -----------\")\n", "            use_web_search = True\n", "\n", "        print(\"=\" * 20, 'Document Validation Completed', \"=\" * 20)\n", "        return {\n", "            \"documents\": filtered_docs,\n", "            \"use_web_search\": use_web_search,\n", "            \"question\": question,\n", "        }\n"]}, {"cell_type": "code", "execution_count": 133, "id": "ccb28d0c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================== Document Validation Tool Invoked ====================\n", "---------- Relevant Document -----------\n", "[ValidateDocument(binary_score='yes')]\n", "==================== Document Validation Completed ====================\n", "{'documents': [ValidateDocument(binary_score='yes')], 'use_web_search': False, 'question': ' switchgear and transformers'}\n"]}], "source": ["state ={'documents': 'The provided context discusses ABB Switchgear & Transformer Termination, which is an oil-filled plug-in termination for gas-insulated switchgear and transformers up to 420 kV.',\n", " 'question': ' switchgear and transformers'}\n", "\n", "validator = DocumentValidatorv1(llm)\n", "validated_state = validator.validate_documents(state)\n", "\n", "print(validated_state)"]}, {"cell_type": "markdown", "id": "32bf4bf9", "metadata": {}, "source": ["## Hllucination"]}, {"cell_type": "code", "execution_count": 25, "id": "235e9590", "metadata": {}, "outputs": [], "source": ["hallucination_prompt = \"\"\"\n", "You are a grader assessing whether an LLM generation is grounded in/supported by a set of retrieved facts.\n", "Give a binary score 'yes' or 'no'. 'Yes' means that the answer is grounded in the facts.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 117, "id": "65b63374", "metadata": {}, "outputs": [], "source": ["from typing import Dict\n", "from pydantic import BaseModel, Field, constr\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.runnables import RunnableSequence\n", "from langchain.llms import BaseLLM\n", "\n", "\n", "# Hallucination Grader\n", "class GradeHallucinations(BaseModel):\n", "    \"\"\"Binary score for hallucination present in generated answer.\"\"\"\n", "    binary_score: str= Field(...,\n", "        description=\"Is the answer grounded in the given facts? 'yes' or 'no'.\"\n", "    )\n", "    explanation: str = Field(...,\n", "        description=\"Explain why the answer is or isn't grounded in the facts.\"\n", "    )\n", "\n", "\n", "class HallucinationGrader:\n", "    def __init__(self, llm: BaseLLM):\n", "        self.llm = llm\n", "        self.structured_llm_grader = self.llm.with_structured_output(GradeHallucinations)\n", "        \n", "        self.prompt = ChatPromptTemplate.from_messages([\n", "            (\"system\", hallucination_prompt),\n", "            (\"human\", \"Set of facts:\\n\\n{documents}\\n\\nLLM generation:\\n\\n{generation}\")\n", "        ])\n", "        \n", "        self.chain: RunnableSequence = self.prompt | self.structured_llm_grader\n", "\n", "    def grade_hallucination(self, state: GraphState) -> Dict[str, Any]:\n", "        print(\"=\" * 20, 'Hallucination Grading', \"=\" * 20)\n", "        documents = \"\\n\".join([doc for doc in state[\"documents\"]])\n", "        generation = state[\"generation\"]\n", "        \n", "        result = self.chain.invoke({\n", "            \"documents\": documents,\n", "            \"generation\": generation\n", "        })\n", "        \n", "        print(f\"Hallucination check: {result.binary_score}\")\n", "        print(f\"Explanation: {result.explanation}\")\n", "        \n", "        if result.binary_score.lower() == \"yes\":\n", "            print(\"Generation is grounded in facts\")\n", "            return {**state}\n", "        else:\n", "            print(\"Generation contains hallucinations - triggering web search\")\n", "            return {**state, \"use_web_search\": True}\n"]}, {"cell_type": "code", "execution_count": 42, "id": "11e6359e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================== Hallucination Grading ====================\n", "Hallucination check: yes\n", "Explanation: The answer is grounded in the facts, as the definition of computing provided is accurate and supported by the given context.\n", "Generation is grounded in facts\n", "<__main__.HallucinationGrader object at 0x0000019393F06BD0>\n"]}], "source": ["hallucination =HallucinationGrader(llm)\n", "hallucination.grade_hallucination(updated_state)\n", "print(hallucination)"]}, {"cell_type": "markdown", "id": "474a7ab9", "metadata": {}, "source": ["## Router"]}, {"cell_type": "code", "execution_count": null, "id": "b8a86a34", "metadata": {}, "outputs": [], "source": ["\n", "message = \"\"\"You are an expert at routing a user question to a vectorstore or web search.\n", "The vectorstore contains documents related to HighVoltage and RAG.\n", "Use the vectorstore for questions on these topics. For ANY other question, choose web-search route.\"\"\""]}, {"cell_type": "code", "execution_count": 75, "id": "8da8cf40", "metadata": {}, "outputs": [], "source": ["# test\n", "from typing import Literal\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.pydantic_v1 import BaseModel, Field\n", "\n", "\n", "\n", "\n", "# Question Router\n", "class RouteQuery(BaseModel):\n", "    \"\"\"Route a user query to the most relevant data source.\"\"\"\n", "    datasource: Literal[\"vectorstore\", \"web_search\"] = Field(\n", "        ...,\n", "        description=\"Route the user query to the vectorstore or websearch. Avalable options are 'vectorstore' or 'web_search'\",\n", "    )\n", "\n", "class QuestionRouter:\n", "\tdef __init__(self, llm: ChatGroq):\n", "\t\tself.llm = llm \n", "\t\tself.structured_llm_router = llm.with_structured_output(RouteQuery)\n", "\t\trouter_prompt = ChatPromptTemplate.from_messages(\n", "\t\t\t[(\"system\", message), (\"human\", \"{question}\")]\n", "\t\t)\n", "\n", "\t\tself.question_router = router_prompt | self.structured_llm_router\n", "\n", "\tdef route_question(self, state: GraphState):\n", "\t\tprint(\"---ROUTE QUESTION---\")\n", "\t\tquestion = state[\"question\"]\n", "\n", "\t\tsource = self.question_router.invoke({\"question\": question})\n", "\n", "\t\tif source.datasource == \"web_search\":\n", "\t\t\tprint(\"Routing to web search\")\n", "\t\t\treturn {**state, \"use_web_search\": True}\n", "\t\telse:\n", "\t\t\tprint(\"Routing to vectorstore\")\n", "\t\t\treturn {**state, \"use_web_search\": False}"]}, {"cell_type": "markdown", "id": "1eaaf81d", "metadata": {}, "source": ["# Manual Testing"]}, {"cell_type": "markdown", "id": "b0c3add5", "metadata": {}, "source": ["## Web_search worflow testing"]}, {"cell_type": "code", "execution_count": 76, "id": "1d3c7bbb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---ROUTE QUESTION---\n", "Routing to web search\n", "Manual Testing: \t\n", "{'question': 'What is  computing?', 'documents': [], 'use_web_search': True}\n", "==================== Web Search Tool Invoked ====================\n", "==================== Web Search Tool Completed ====================\n", "======= search response ========\n", "{'documents': [Document(metadata={}, page_content='to pin down, but the Computing Curricula 2005: The Overview Report prepared by a joint committee of ACM, IEEE, and AIS gives the following definition: \"In a general way, we can define computing to mean any goal-oriented activity requiring, benefiting from, or creating computers.‖ This is a very broad definition that comprises the development of computer hardware, the use of computer applications, and the development of computer software. This text focuses on the last of these enterprises, the [...] way because they vary from place to place and they change over time. In a similar way, entering the field of computing can be disorienting and finding clear definitions of its features can be difficult. Still, there are fundamental concepts that underlie the field of computing that can be articulated, learned and deployed effectively. All computing is based on the coordinated use of computer devices, called hardware, and the computer programs that drive them, called software, and all software [...] applications are built using data and process specifications, called data structures and algorithms. These fundamentals have remained remarkably stable over the history of computing, in spite of the continual advance of the hardware and software technologies, and the continual development of new paradigms for data and process specifications. This chapter defines the notion of computing, discusses the concepts of hardware and software, and concludes with an introduction to the development of\\nTo make it easy, computing includes the simplest actions, such as sending emails or surfing the internet, running applications on smartphones, and more sophisticated activities like developing software and making use of machines to perform simulations, process large volumes of data, and use artificial intelligence to automate any kind of work. [...] The use of computers or other digital devices for performance, problem-solving, or even information management. It involves using programs and applications in software as well as hardware, such as computers, servers, and networks, to perform a vast array of tasks, ranging from routine numerical calculations to complex data analysis. [...] In this article, let’s have a close look at the most significant types of computing and how they apply in daily life and across fields. Whether for personal use, management of any business, or solving complex problems in science and technology, computing has now taken a central role in deciding how we live, work, and relate to our surroundings.\\n\\nWhat is Computing?\\n----------------------\\nA computer is a machine that manipulates data \"Data (computing)\") according to a set of instructions called a computer program.( The program has an executable form that the computer can use directly to execute the instructions. The same program in its human-readable source code form, enables a programmer to study and develop a sequence of steps known as an algorithm.( Because the instructions can be carried out in different types of computers, a single set of source instructions converts to [...] Computer programming is the process of writing, testing, debugging, and maintaining the source code and documentation of computer programs. This source code is written in a programming language, which is an artificial language that is often more restrictive than natural languages, but easily translated by the computer. Programming is used to invoke some desired behavior (customization) from the machine.( [...] Information technology (IT) is the application of computers and telecommunications equipment to store, retrieve, transmit, and manipulate data,( often in the context of a business or other enterprise.( The term is commonly used as a synonym for computers and computer networks, but also encompasses other information distribution technologies such as television and telephones. Several industries are associated with information technology, including computer hardware, software, electronics,\\nA computer might be described with deceptive simplicity as “an apparatus that performs routine calculations automatically.” Such a definition would owe its deceptiveness to a naive and narrow view of calculation as a strictly mathematical process. In fact, calculation underlies many activities that are not normally thought of as mathematical. Walking across a room, for instance, requires many complex, albeit subconscious, calculations. Computers, too, have proved capable of solving a vast array\\nit is the possibility of the machine operating on and modifying its own program. (In London in 1947, in the course of what was, so far as is known, the earliest public lecture to mention computer intelligence, Turing said, ‘What we want is a machine that can learn from experience’, adding that the ‘possibility of letting the machine alter its own instructions provides the mechanism for this’ (Turing \\\\[1947\\\\] p. 393). Turing\\'s computing machine of 1936 is now known simply as the universal Turing [...] computing machines (1) learning from experience and (2) solving problems by means of searching through the space of possible solutions, guided by rule-of-thumb principles (Michie in interview with Copeland, 1995). The modern term for the latter idea is ‘heuristic search’, a heuristic being any rule-of-thumb principle that cuts down the amount of searching required in order to find a solution to a problem. At Bletchley Park Turing illustrated his ideas on machine intelligence by reference to [...] computer, is set up for a new task by re-routing wires, by means of plugs etc.)')], 'question': 'What is  computing?'}\n", "Generation component loaded!\n", "==================== Generation Tool Invoked ====================\n", "==================== Generation Completed ====================\n", "\n", "Final Output:\n", " {'generation': 'According to the provided context, computing is defined as \"any goal-oriented activity requiring, benefiting from, or creating computers.\" This broad definition encompasses the development of computer hardware, the use of computer applications, and the development of computer software. In simpler terms, computing involves using computers or other digital devices to perform various tasks, ranging from routine calculations to complex data analysis, and includes activities such as sending emails, surfing the internet, and developing software.', 'documents': [Document(metadata={}, page_content='to pin down, but the Computing Curricula 2005: The Overview Report prepared by a joint committee of ACM, IEEE, and AIS gives the following definition: \"In a general way, we can define computing to mean any goal-oriented activity requiring, benefiting from, or creating computers.‖ This is a very broad definition that comprises the development of computer hardware, the use of computer applications, and the development of computer software. This text focuses on the last of these enterprises, the [...] way because they vary from place to place and they change over time. In a similar way, entering the field of computing can be disorienting and finding clear definitions of its features can be difficult. Still, there are fundamental concepts that underlie the field of computing that can be articulated, learned and deployed effectively. All computing is based on the coordinated use of computer devices, called hardware, and the computer programs that drive them, called software, and all software [...] applications are built using data and process specifications, called data structures and algorithms. These fundamentals have remained remarkably stable over the history of computing, in spite of the continual advance of the hardware and software technologies, and the continual development of new paradigms for data and process specifications. This chapter defines the notion of computing, discusses the concepts of hardware and software, and concludes with an introduction to the development of\\nTo make it easy, computing includes the simplest actions, such as sending emails or surfing the internet, running applications on smartphones, and more sophisticated activities like developing software and making use of machines to perform simulations, process large volumes of data, and use artificial intelligence to automate any kind of work. [...] The use of computers or other digital devices for performance, problem-solving, or even information management. It involves using programs and applications in software as well as hardware, such as computers, servers, and networks, to perform a vast array of tasks, ranging from routine numerical calculations to complex data analysis. [...] In this article, let’s have a close look at the most significant types of computing and how they apply in daily life and across fields. Whether for personal use, management of any business, or solving complex problems in science and technology, computing has now taken a central role in deciding how we live, work, and relate to our surroundings.\\n\\nWhat is Computing?\\n----------------------\\nA computer is a machine that manipulates data \"Data (computing)\") according to a set of instructions called a computer program.( The program has an executable form that the computer can use directly to execute the instructions. The same program in its human-readable source code form, enables a programmer to study and develop a sequence of steps known as an algorithm.( Because the instructions can be carried out in different types of computers, a single set of source instructions converts to [...] Computer programming is the process of writing, testing, debugging, and maintaining the source code and documentation of computer programs. This source code is written in a programming language, which is an artificial language that is often more restrictive than natural languages, but easily translated by the computer. Programming is used to invoke some desired behavior (customization) from the machine.( [...] Information technology (IT) is the application of computers and telecommunications equipment to store, retrieve, transmit, and manipulate data,( often in the context of a business or other enterprise.( The term is commonly used as a synonym for computers and computer networks, but also encompasses other information distribution technologies such as television and telephones. Several industries are associated with information technology, including computer hardware, software, electronics,\\nA computer might be described with deceptive simplicity as “an apparatus that performs routine calculations automatically.” Such a definition would owe its deceptiveness to a naive and narrow view of calculation as a strictly mathematical process. In fact, calculation underlies many activities that are not normally thought of as mathematical. Walking across a room, for instance, requires many complex, albeit subconscious, calculations. Computers, too, have proved capable of solving a vast array\\nit is the possibility of the machine operating on and modifying its own program. (In London in 1947, in the course of what was, so far as is known, the earliest public lecture to mention computer intelligence, Turing said, ‘What we want is a machine that can learn from experience’, adding that the ‘possibility of letting the machine alter its own instructions provides the mechanism for this’ (Turing \\\\[1947\\\\] p. 393). Turing\\'s computing machine of 1936 is now known simply as the universal Turing [...] computing machines (1) learning from experience and (2) solving problems by means of searching through the space of possible solutions, guided by rule-of-thumb principles (Michie in interview with Copeland, 1995). The modern term for the latter idea is ‘heuristic search’, a heuristic being any rule-of-thumb principle that cuts down the amount of searching required in order to find a solution to a problem. At Bletchley Park Turing illustrated his ideas on machine intelligence by reference to [...] computer, is set up for a new task by re-routing wires, by means of plugs etc.)')], 'question': 'What is  computing?'}\n", "==================== Hallucination Grading ====================\n", "Hallucination check: yes\n", "Explanation: The answer is grounded in the provided facts, as it accurately summarizes the definition of computing and its applications, and is supported by the given text.\n", "Generation is grounded in facts\n", "<__main__.HallucinationGrader object at 0x0000019396E12B50>\n"]}], "source": ["state = {\n", "    \"question\": \"What is  computing?\",\n", "    \"documents\": []\n", "}\n", "router = QuestionRouter(llm)\n", "router_response = router.route_question(state)\n", "print(\"Manual Testing: \\t\")\n", "print(router_response)\n", "\n", "\n", "search_tool = TaivilySearchTool()\n", "search_response = search_tool.web_search(router_response)\n", "print(\"======= search response ========\")\n", "\n", "print(search_response)\n", "\n", "updated_state = GenerationTool(llm=llm).generate(search_response)\n", "print(\"\\nFinal Output:\\n\", updated_state)\n", "\n", "hallucination = HallucinationGrader(llm)\n", "hallucination.grade_hallucination(updated_state)\n", "print(hallucination)"]}, {"cell_type": "markdown", "id": "7846f19e", "metadata": {}, "source": ["## vectore search workflow testing"]}, {"cell_type": "code", "execution_count": 115, "id": "********", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---ROUTE QUESTION---\n", "Routing to web search\n", "Manual Testing: \t\n", "{'documents': 'The provided context discusses ABB Switchgear & Transformer Termination, which is an oil-filled plug-in termination for gas-insulated switchgear and transformers up to 420 kV.', 'question': ' switchgear and transformers', 'use_web_search': True}\n", "Loading components...\n", "  Loading vector database...\n", "Components loaded!\n", "  Loading vector database...\n", "====================  Retriever Tool Invoked ====================\n", " switchgear and transformers\n", "🔍 Retrieving documents for: ' switchgear and transformers...'\n", "Rag Tool Response: The provided context describes cable accessories for switchgear and transformers, including oil-filled plug-in terminations and dry-type plug-in terminations. These terminations are designed for installation in gas-insulated switchgear, transformers, or oil-filled cable boxes, and are suitable for operation up to 420 kV.\n", "Time Take: 3.****************\n", "====================  Retriever Tool Invoked Completed ====================\n", "======= Rag response ========\n", "{'documents': 'The provided context describes cable accessories for switchgear and transformers, including oil-filled plug-in terminations and dry-type plug-in terminations. These terminations are designed for installation in gas-insulated switchgear, transformers, or oil-filled cable boxes, and are suitable for operation up to 420 kV.', 'question': ' switchgear and transformers'}\n"]}], "source": ["state = {'documents': 'The provided context discusses ABB Switchgear & Transformer Termination, which is an oil-filled plug-in termination for gas-insulated switchgear and transformers up to 420 kV.',\n", " 'question': ' switchgear and transformers'}\n", "router = QuestionRouter(llm)\n", "router_response = router.route_question(state)\n", "print(\"Manual Testing: \\t\")\n", "print(router_response)\n", "\n", "\n", "rag_tool = RetrieverTool(llm)\n", "search_response = rag_tool.search(router_response)\n", "print(\"======= Rag response ========\")\n", "print(search_response)"]}, {"cell_type": "code", "execution_count": 118, "id": "35e687f0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================== Document Validation Tool Invoked ====================\n", "---------- Relevant Document -----------\n", "[ValidateDocument(binary_score='yes')]\n", "==================== Document Validation Completed ====================\n", "======= vali response ========\n", "Validation Result \t: {'documents': [ValidateDocument(binary_score='yes')], 'use_web_search': False, 'question': ' switchgear and transformers'}\n", "Generation component loaded!\n", "==================== Generation Tool Invoked ====================\n", "==================== Generation Completed ====================\n", "\n", "Final Output:\n", " {'generation': 'Based on the provided context, I can help you with questions related to switchgear and transformers.\\n\\nSwitchgear and Transformers:\\n\\n* Switchgear: A term used to describe electrical equipment that controls the flow of electrical current, including circuit breakers, switches, and fuses.\\n* Transformers: A device that transfers electrical energy from one circuit to another through electromagnetic induction, used to increase or decrease voltage levels.\\n\\nIf you have a specific question or need further clarification, please feel free to ask!', 'documents': 'The provided context describes cable accessories for switchgear and transformers, including oil-filled plug-in terminations and dry-type plug-in terminations. These terminations are designed for installation in gas-insulated switchgear, transformers, or oil-filled cable boxes, and are suitable for operation up to 420 kV.', 'question': ' switchgear and transformers'}\n", "======= gen response ========\n", "==================== Hallucination Grading ====================\n", "Hallucination check: yes\n", "Explanation: The answer is grounded in the provided facts, which include information about switchgear and transformers. The LLM generation provides a clear and concise definition of switchgear and transformers, and offers to help with questions related to the topic. The answer does not contain any hallucinations or ungrounded information.\n", "Generation is grounded in facts\n", "<__main__.HallucinationGrader object at 0x00000193B7B00E50>\n"]}], "source": ["rag_validation = DocumentValidatorv1(llm)\n", "vali_response = rag_validation.validate_documents(search_response)\n", "print(\"======= vali response ========\")\n", "print(f\"Validation Result \\t: {vali_response}\")\n", "\n", "updated_state = GenerationTool(llm=llm).generate(search_response)\n", "print(\"\\nFinal Output:\\n\", updated_state)\n", "print(\"======= gen response ========\")\n", "\n", "hallucination = HallucinationGrader(llm)\n", "hallucination.grade_hallucination(updated_state)\n", "print(hallucination)"]}, {"cell_type": "code", "execution_count": 134, "id": "5a58b407", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---ROUTE QUESTION---\n", "Routing to web search\n", "Manual Testing: \t\n", "{'documents': 'The provided context discusses ABB Switchgear & Transformer Termination, which is an oil-filled plug-in termination for gas-insulated switchgear and transformers up to 420 kV.', 'question': ' switchgear and transformers', 'use_web_search': True}\n", "Loading components...\n", "  Loading vector database...\n", "Components loaded!\n", "  Loading vector database...\n", "====================  Retriever Tool Invoked ====================\n", " switchgear and transformers\n", "🔍 Retrieving documents for: ' switchgear and transformers...'\n", "Rag Tool Response: The context discusses switchgear and transformers, specifically mentioning cable accessories for underground transmission solutions, such as oil-filled plug-in terminations and dry-type plug-in terminations.\n", "Time Take: 5.***************\n", "====================  Retriever Tool Invoked Completed ====================\n", "======= Rag response ========\n", "{'documents': 'The context discusses switchgear and transformers, specifically mentioning cable accessories for underground transmission solutions, such as oil-filled plug-in terminations and dry-type plug-in terminations.', 'question': ' switchgear and transformers'}\n", "==================== Document Validation Tool Invoked ====================\n", "---------- Relevant Document -----------\n", "[ValidateDocument(binary_score='yes')]\n", "==================== Document Validation Completed ====================\n", "{'documents': [ValidateDocument(binary_score='yes')], 'use_web_search': False, 'question': ' switchgear and transformers'}\n"]}], "source": ["state ={'documents': 'The provided context discusses ABB Switchgear & Transformer Termination, which is an oil-filled plug-in termination for gas-insulated switchgear and transformers up to 420 kV.',\n", " 'question': ' switchgear and transformers'}\n", "\n", "router = QuestionRouter(llm)\n", "router_response = router.route_question(state)\n", "print(\"Manual Testing: \\t\")\n", "print(router_response)\n", "\n", "\n", "rag_tool = RetrieverTool(llm)\n", "search_response = rag_tool.search(router_response)\n", "print(\"======= Rag response ========\")\n", "print(search_response)\n", "\n", "validator = DocumentValidatorv1(llm)\n", "validated_state = validator.validate_documents(state)\n", "\n", "print(validated_state)"]}, {"cell_type": "code", "execution_count": null, "id": "ff5e648e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "e23befaa", "metadata": {}, "source": ["# Agent"]}, {"cell_type": "markdown", "id": "129d0103", "metadata": {}, "source": ["## Agent exp -1"]}, {"cell_type": "code", "execution_count": 28, "id": "836b5697", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph, END"]}, {"cell_type": "code", "execution_count": 36, "id": "5fcc9778", "metadata": {}, "outputs": [], "source": ["# Main RAG Agent Workflow\n", "class RAGAgent:\n", "    def __init__(self):\n", "        # Initialize LLM\n", "        self.llm = ChatGroq(\n", "            model=\"llama3-8b-8192\",\n", "            api_key=os.getenv(\"GROQ_API_KEY\")\n", "        )\n", "        \n", "        # Initialize tools\n", "        self.web_search_tool = TaivilySearchTool()\n", "        self.generation_tool = GenerationTool(self.llm)\n", "        self.document_validator = DocumentValidator(self.llm)\n", "        self.hallucination_grader = HallucinationGrader(self.llm)\n", "        self.question_router = QuestionRouter(self.llm)\n", "        \n", "        # Build the graph\n", "        self.workflow = self._build_graph()\n", "\n", "    def _build_graph(self):\n", "        workflow = StateGraph(GraphState)\n", "        \n", "        # Add nodes\n", "        workflow.add_node(\"route_question\", self.question_router.route_question)\n", "        workflow.add_node(\"web_search\", self.web_search_tool.web_search)\n", "        workflow.add_node(\"validate_documents\", self.document_validator.validate_documents)\n", "        workflow.add_node(\"generate\", self.generation_tool.generate)\n", "        workflow.add_node(\"grade_hallucination\", self.hallucination_grader.grade_hallucination)\n", "        \n", "        # Define the flow\n", "        workflow.set_entry_point(\"route_question\")\n", "        \n", "        # Conditional edges\n", "        workflow.add_conditional_edges(\n", "            \"route_question\",\n", "            self._decide_next_step,\n", "            {\n", "                \"web_search\": \"web_search\",\n", "                \"validate_documents\": \"validate_documents\"\n", "            }\n", "        )\n", "        \n", "        workflow.add_edge(\"web_search\", \"generate\")\n", "        \n", "        workflow.add_conditional_edges(\n", "            \"validate_documents\",\n", "            self._decide_after_validation,\n", "            {\n", "                \"web_search\": \"web_search\",\n", "                \"generate\": \"generate\"\n", "            }\n", "        )\n", "        \n", "        workflow.add_edge(\"generate\", \"grade_hallucination\")\n", "        \n", "        workflow.add_conditional_edges(\n", "            \"grade_hallucination\",\n", "            self._decide_after_hallucination_check,\n", "            {\n", "                \"web_search\": \"web_search\",\n", "                \"end\": END\n", "            }\n", "        )\n", "        \n", "        return workflow.compile()\n", "\n", "    def _decide_next_step(self, state: GraphState) -> str:\n", "        if state.get(\"use_web_search\", False):\n", "            return \"web_search\"\n", "        return \"validate_documents\"\n", "\n", "    def _decide_after_validation(self, state: GraphState) -> str:\n", "        if state.get(\"use_web_search\", False):\n", "            return \"web_search\"\n", "        return \"generate\"\n", "\n", "    def _decide_after_hallucination_check(self, state: GraphState) -> str:\n", "        if state.get(\"use_web_search\", False):\n", "            return \"web_search\"\n", "        return \"end\"\n", "\n", "    def run(self, question: str, documents: List[Document] = None) -> Dict[str, Any]:\n", "        \"\"\"Run the RAG agent workflow\"\"\"\n", "        initial_state = {\n", "            \"question\": question,\n", "            \"generation\": \"\",\n", "            \"use_web_search\": <PERSON><PERSON><PERSON>,\n", "            \"documents\": documents or []\n", "        }\n", "        \n", "        result = self.workflow.invoke(initial_state)\n", "        return result\n"]}, {"cell_type": "code", "execution_count": 37, "id": "bc8e0876", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generation component loaded!\n", "==================== Question Routing ====================\n", "Routing to vectorstore\n", "==================== Document Validation Tool Invoked ====================\n", "==================== Document Validation Completed ====================\n", "==================== Generation Tool Invoked ====================\n", "==================== Generation Completed ====================\n", "==================== Hallucination Grading ====================\n"]}, {"ename": "BadRequestError", "evalue": "Error code: 400 - {'error': {'message': \"Failed to call a function. Please adjust your prompt. See 'failed_generation' for more details.\", 'type': 'invalid_request_error', 'code': 'tool_use_failed', 'failed_generation': '{\"tool_use\":{\"tool_calls\":[{\"id\":\"pending\",\"type\":\"function\",\"function\":{\"name\":\"GradeHallucinations\"},\"parameters\":{\"binary_score\":\"yes\",\"explanation\":\"The answer is grounded in the facts provided, which are the latest advancements in machine learning. The LLM generation provides a clear and concise summary of the key developments in machine learning, and it is supported by the facts.\"}}]}}'}}", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mBadRequestError\u001b[39m                           <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[37]\u001b[39m\u001b[32m, line 8\u001b[39m\n\u001b[32m      5\u001b[39m question = \u001b[33m\"\u001b[39m\u001b[33mWhat are the latest developments in machine learning?\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m      7\u001b[39m \u001b[38;5;66;03m# Run the workflow\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m8\u001b[39m result = \u001b[43magent\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[43mquestion\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     10\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m\"\u001b[39m + \u001b[33m\"\u001b[39m\u001b[33m=\u001b[39m\u001b[33m\"\u001b[39m*\u001b[32m50\u001b[39m)\n\u001b[32m     11\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mFINAL RESULT:\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[36]\u001b[39m\u001b[32m, line 91\u001b[39m, in \u001b[36mRAGAgent.run\u001b[39m\u001b[34m(self, question, documents)\u001b[39m\n\u001b[32m     83\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"Run the RAG agent workflow\"\"\"\u001b[39;00m\n\u001b[32m     84\u001b[39m initial_state = {\n\u001b[32m     85\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mquestion\u001b[39m\u001b[33m\"\u001b[39m: question,\n\u001b[32m     86\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mgeneration\u001b[39m\u001b[33m\"\u001b[39m: \u001b[33m\"\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m     87\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33muse_web_search\u001b[39m\u001b[33m\"\u001b[39m: \u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[32m     88\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mdocuments\u001b[39m\u001b[33m\"\u001b[39m: documents \u001b[38;5;129;01mor\u001b[39;00m []\n\u001b[32m     89\u001b[39m }\n\u001b[32m---> \u001b[39m\u001b[32m91\u001b[39m result = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mworkflow\u001b[49m\u001b[43m.\u001b[49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\u001b[43minitial_state\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     92\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m result\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\site-packages\\langgraph\\pregel\\__init__.py:2719\u001b[39m, in \u001b[36mPregel.invoke\u001b[39m\u001b[34m(self, input, config, stream_mode, output_keys, interrupt_before, interrupt_after, checkpoint_during, debug, **kwargs)\u001b[39m\n\u001b[32m   2716\u001b[39m chunks: \u001b[38;5;28mlist\u001b[39m[Union[\u001b[38;5;28mdict\u001b[39m[\u001b[38;5;28mstr\u001b[39m, Any], Any]] = []\n\u001b[32m   2717\u001b[39m interrupts: \u001b[38;5;28mlist\u001b[39m[Interrupt] = []\n\u001b[32m-> \u001b[39m\u001b[32m2719\u001b[39m \u001b[43m\u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mchunk\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   2720\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m   2721\u001b[39m \u001b[43m    \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2722\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstream_mode\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream_mode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2723\u001b[39m \u001b[43m    \u001b[49m\u001b[43moutput_keys\u001b[49m\u001b[43m=\u001b[49m\u001b[43moutput_keys\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2724\u001b[39m \u001b[43m    \u001b[49m\u001b[43minterrupt_before\u001b[49m\u001b[43m=\u001b[49m\u001b[43minterrupt_before\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2725\u001b[39m \u001b[43m    \u001b[49m\u001b[43minterrupt_after\u001b[49m\u001b[43m=\u001b[49m\u001b[43minterrupt_after\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2726\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcheckpoint_during\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcheckpoint_during\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2727\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdebug\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdebug\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2728\u001b[39m \u001b[43m    \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2729\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m   2730\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mstream_mode\u001b[49m\u001b[43m \u001b[49m\u001b[43m==\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mvalues\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\n\u001b[32m   2731\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   2732\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;28;43misinstance\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mchunk\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mdict\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m   2733\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;129;43;01mand\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43m(\u001b[49m\u001b[43mints\u001b[49m\u001b[43m \u001b[49m\u001b[43m:=\u001b[49m\u001b[43m \u001b[49m\u001b[43mchunk\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[43mINTERRUPT\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mis\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mnot\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\n\u001b[32m   2734\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\site-packages\\langgraph\\pregel\\__init__.py:2436\u001b[39m, in \u001b[36mPregel.stream\u001b[39m\u001b[34m(self, input, config, stream_mode, output_keys, interrupt_before, interrupt_after, checkpoint_during, debug, subgraphs)\u001b[39m\n\u001b[32m   2434\u001b[39m         \u001b[38;5;28;01mfor\u001b[39;00m task \u001b[38;5;129;01min\u001b[39;00m loop.match_cached_writes():\n\u001b[32m   2435\u001b[39m             loop.output_writes(task.id, task.writes, cached=\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[32m-> \u001b[39m\u001b[32m2436\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43m_\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mrunner\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtick\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   2437\u001b[39m \u001b[43m            \u001b[49m\u001b[43m[\u001b[49m\u001b[43mt\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mt\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mloop\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtasks\u001b[49m\u001b[43m.\u001b[49m\u001b[43mvalues\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mnot\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mt\u001b[49m\u001b[43m.\u001b[49m\u001b[43mwrites\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2438\u001b[39m \u001b[43m            \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mstep_timeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2439\u001b[39m \u001b[43m            \u001b[49m\u001b[43mget_waiter\u001b[49m\u001b[43m=\u001b[49m\u001b[43mget_waiter\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2440\u001b[39m \u001b[43m            \u001b[49m\u001b[43mschedule_task\u001b[49m\u001b[43m=\u001b[49m\u001b[43mloop\u001b[49m\u001b[43m.\u001b[49m\u001b[43maccept_push\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2441\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m   2442\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;66;43;03m# emit output\u001b[39;49;00m\n\u001b[32m   2443\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;28;43;01myield from\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43moutput\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   2444\u001b[39m \u001b[38;5;66;03m# emit output\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\site-packages\\langgraph\\pregel\\runner.py:161\u001b[39m, in \u001b[36mPregelRunner.tick\u001b[39m\u001b[34m(self, tasks, reraise, timeout, retry_policy, get_waiter, schedule_task)\u001b[39m\n\u001b[32m    159\u001b[39m t = tasks[\u001b[32m0\u001b[39m]\n\u001b[32m    160\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m161\u001b[39m     \u001b[43mrun_with_retry\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    162\u001b[39m \u001b[43m        \u001b[49m\u001b[43mt\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    163\u001b[39m \u001b[43m        \u001b[49m\u001b[43mretry_policy\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    164\u001b[39m \u001b[43m        \u001b[49m\u001b[43mconfigurable\u001b[49m\u001b[43m=\u001b[49m\u001b[43m{\u001b[49m\n\u001b[32m    165\u001b[39m \u001b[43m            \u001b[49m\u001b[43mCONFIG_KEY_CALL\u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mpartial\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    166\u001b[39m \u001b[43m                \u001b[49m\u001b[43m_call\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    167\u001b[39m \u001b[43m                \u001b[49m\u001b[43mweakref\u001b[49m\u001b[43m.\u001b[49m\u001b[43mref\u001b[49m\u001b[43m(\u001b[49m\u001b[43mt\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    168\u001b[39m \u001b[43m                \u001b[49m\u001b[43mretry\u001b[49m\u001b[43m=\u001b[49m\u001b[43mretry_policy\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    169\u001b[39m \u001b[43m                \u001b[49m\u001b[43mfutures\u001b[49m\u001b[43m=\u001b[49m\u001b[43mweakref\u001b[49m\u001b[43m.\u001b[49m\u001b[43mref\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfutures\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    170\u001b[39m \u001b[43m                \u001b[49m\u001b[43mschedule_task\u001b[49m\u001b[43m=\u001b[49m\u001b[43mschedule_task\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    171\u001b[39m \u001b[43m                \u001b[49m\u001b[43msubmit\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43msubmit\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    172\u001b[39m \u001b[43m            \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    173\u001b[39m \u001b[43m        \u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    174\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    175\u001b[39m     \u001b[38;5;28mself\u001b[39m.commit(t, \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[32m    176\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\site-packages\\langgraph\\pregel\\retry.py:40\u001b[39m, in \u001b[36mrun_with_retry\u001b[39m\u001b[34m(task, retry_policy, configurable)\u001b[39m\n\u001b[32m     38\u001b[39m     task.writes.clear()\n\u001b[32m     39\u001b[39m     \u001b[38;5;66;03m# run the task\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m40\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mtask\u001b[49m\u001b[43m.\u001b[49m\u001b[43mproc\u001b[49m\u001b[43m.\u001b[49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtask\u001b[49m\u001b[43m.\u001b[49m\u001b[43minput\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     41\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m ParentCommand \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[32m     42\u001b[39m     ns: \u001b[38;5;28mstr\u001b[39m = config[CONF][CONFIG_KEY_CHECKPOINT_NS]\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\site-packages\\langgraph\\utils\\runnable.py:623\u001b[39m, in \u001b[36mRunnableSeq.invoke\u001b[39m\u001b[34m(self, input, config, **kwargs)\u001b[39m\n\u001b[32m    621\u001b[39m     \u001b[38;5;66;03m# run in context\u001b[39;00m\n\u001b[32m    622\u001b[39m     \u001b[38;5;28;01mwith\u001b[39;00m set_config_context(config, run) \u001b[38;5;28;01mas\u001b[39;00m context:\n\u001b[32m--> \u001b[39m\u001b[32m623\u001b[39m         \u001b[38;5;28minput\u001b[39m = \u001b[43mcontext\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[43mstep\u001b[49m\u001b[43m.\u001b[49m\u001b[43minvoke\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    624\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    625\u001b[39m     \u001b[38;5;28minput\u001b[39m = step.invoke(\u001b[38;5;28minput\u001b[39m, config)\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\site-packages\\langgraph\\utils\\runnable.py:377\u001b[39m, in \u001b[36mRunnableCallable.invoke\u001b[39m\u001b[34m(self, input, config, **kwargs)\u001b[39m\n\u001b[32m    375\u001b[39m         run_manager.on_chain_end(ret)\n\u001b[32m    376\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m377\u001b[39m     ret = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    378\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.recurse \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(ret, Runnable):\n\u001b[32m    379\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m ret.invoke(\u001b[38;5;28minput\u001b[39m, config)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[26]\u001b[39m\u001b[32m, line 36\u001b[39m, in \u001b[36mHallucinationGrader.grade_hallucination\u001b[39m\u001b[34m(self, state)\u001b[39m\n\u001b[32m     33\u001b[39m documents = \u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m\"\u001b[39m.join([doc.page_content \u001b[38;5;28;01mfor\u001b[39;00m doc \u001b[38;5;129;01min\u001b[39;00m state[\u001b[33m\"\u001b[39m\u001b[33mdocuments\u001b[39m\u001b[33m\"\u001b[39m]])\n\u001b[32m     34\u001b[39m generation = state[\u001b[33m\"\u001b[39m\u001b[33mgeneration\u001b[39m\u001b[33m\"\u001b[39m]\n\u001b[32m---> \u001b[39m\u001b[32m36\u001b[39m result = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mchain\u001b[49m\u001b[43m.\u001b[49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\u001b[43m{\u001b[49m\n\u001b[32m     37\u001b[39m \u001b[43m    \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mdocuments\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mdocuments\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     38\u001b[39m \u001b[43m    \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mgeneration\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mgeneration\u001b[49m\n\u001b[32m     39\u001b[39m \u001b[43m\u001b[49m\u001b[43m}\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     41\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mHallucination check: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mresult.binary_score\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m     42\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mExplanation: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mresult.explanation\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\site-packages\\langchain_core\\runnables\\base.py:3046\u001b[39m, in \u001b[36mRunnableSequence.invoke\u001b[39m\u001b[34m(self, input, config, **kwargs)\u001b[39m\n\u001b[32m   3044\u001b[39m                 input_ = context.run(step.invoke, input_, config, **kwargs)\n\u001b[32m   3045\u001b[39m             \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m3046\u001b[39m                 input_ = context.run(step.invoke, input_, config)\n\u001b[32m   3047\u001b[39m \u001b[38;5;66;03m# finish the root run\u001b[39;00m\n\u001b[32m   3048\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\site-packages\\langchain_core\\runnables\\base.py:5434\u001b[39m, in \u001b[36mRunnableBindingBase.invoke\u001b[39m\u001b[34m(self, input, config, **kwargs)\u001b[39m\n\u001b[32m   5427\u001b[39m \u001b[38;5;129m@override\u001b[39m\n\u001b[32m   5428\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34minvoke\u001b[39m(\n\u001b[32m   5429\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m   5432\u001b[39m     **kwargs: Optional[Any],\n\u001b[32m   5433\u001b[39m ) -> Output:\n\u001b[32m-> \u001b[39m\u001b[32m5434\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mbound\u001b[49m\u001b[43m.\u001b[49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   5435\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m   5436\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_merge_configs\u001b[49m\u001b[43m(\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   5437\u001b[39m \u001b[43m        \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43m{\u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   5438\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py:378\u001b[39m, in \u001b[36mBaseChatModel.invoke\u001b[39m\u001b[34m(self, input, config, stop, **kwargs)\u001b[39m\n\u001b[32m    366\u001b[39m \u001b[38;5;129m@override\u001b[39m\n\u001b[32m    367\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34minvoke\u001b[39m(\n\u001b[32m    368\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m    373\u001b[39m     **kwargs: Any,\n\u001b[32m    374\u001b[39m ) -> BaseMessage:\n\u001b[32m    375\u001b[39m     config = ensure_config(config)\n\u001b[32m    376\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m cast(\n\u001b[32m    377\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mChatGeneration\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m--> \u001b[39m\u001b[32m378\u001b[39m         \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mgenerate_prompt\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    379\u001b[39m \u001b[43m            \u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_convert_input\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    380\u001b[39m \u001b[43m            \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    381\u001b[39m \u001b[43m            \u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mcallbacks\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    382\u001b[39m \u001b[43m            \u001b[49m\u001b[43mtags\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtags\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    383\u001b[39m \u001b[43m            \u001b[49m\u001b[43mmetadata\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmetadata\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    384\u001b[39m \u001b[43m            \u001b[49m\u001b[43mrun_name\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mrun_name\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    385\u001b[39m \u001b[43m            \u001b[49m\u001b[43mrun_id\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m.\u001b[49m\u001b[43mpop\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mrun_id\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    386\u001b[39m \u001b[43m            \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    387\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m.generations[\u001b[32m0\u001b[39m][\u001b[32m0\u001b[39m],\n\u001b[32m    388\u001b[39m     ).message\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py:963\u001b[39m, in \u001b[36mBaseChatModel.generate_prompt\u001b[39m\u001b[34m(self, prompts, stop, callbacks, **kwargs)\u001b[39m\n\u001b[32m    954\u001b[39m \u001b[38;5;129m@override\u001b[39m\n\u001b[32m    955\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mgenerate_prompt\u001b[39m(\n\u001b[32m    956\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m    960\u001b[39m     **kwargs: Any,\n\u001b[32m    961\u001b[39m ) -> LLMResult:\n\u001b[32m    962\u001b[39m     prompt_messages = [p.to_messages() \u001b[38;5;28;01mfor\u001b[39;00m p \u001b[38;5;129;01min\u001b[39;00m prompts]\n\u001b[32m--> \u001b[39m\u001b[32m963\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mgenerate\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprompt_messages\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py:782\u001b[39m, in \u001b[36mBaseChatModel.generate\u001b[39m\u001b[34m(self, messages, stop, callbacks, tags, metadata, run_name, run_id, **kwargs)\u001b[39m\n\u001b[32m    779\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m i, m \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(input_messages):\n\u001b[32m    780\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    781\u001b[39m         results.append(\n\u001b[32m--> \u001b[39m\u001b[32m782\u001b[39m             \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_generate_with_cache\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    783\u001b[39m \u001b[43m                \u001b[49m\u001b[43mm\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    784\u001b[39m \u001b[43m                \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    785\u001b[39m \u001b[43m                \u001b[49m\u001b[43mrun_manager\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrun_managers\u001b[49m\u001b[43m[\u001b[49m\u001b[43mi\u001b[49m\u001b[43m]\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mrun_managers\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    786\u001b[39m \u001b[43m                \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    787\u001b[39m \u001b[43m            \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    788\u001b[39m         )\n\u001b[32m    789\u001b[39m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m    790\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m run_managers:\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py:1028\u001b[39m, in \u001b[36mBaseChatModel._generate_with_cache\u001b[39m\u001b[34m(self, messages, stop, run_manager, **kwargs)\u001b[39m\n\u001b[32m   1026\u001b[39m     result = generate_from_stream(\u001b[38;5;28miter\u001b[39m(chunks))\n\u001b[32m   1027\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m inspect.signature(\u001b[38;5;28mself\u001b[39m._generate).parameters.get(\u001b[33m\"\u001b[39m\u001b[33mrun_manager\u001b[39m\u001b[33m\"\u001b[39m):\n\u001b[32m-> \u001b[39m\u001b[32m1028\u001b[39m     result = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_generate\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1029\u001b[39m \u001b[43m        \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrun_manager\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrun_manager\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\n\u001b[32m   1030\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1031\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m   1032\u001b[39m     result = \u001b[38;5;28mself\u001b[39m._generate(messages, stop=stop, **kwargs)\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\site-packages\\langchain_groq\\chat_models.py:557\u001b[39m, in \u001b[36mChatGroq._generate\u001b[39m\u001b[34m(self, messages, stop, run_manager, **kwargs)\u001b[39m\n\u001b[32m    552\u001b[39m message_dicts, params = \u001b[38;5;28mself\u001b[39m._create_message_dicts(messages, stop)\n\u001b[32m    553\u001b[39m params = {\n\u001b[32m    554\u001b[39m     **params,\n\u001b[32m    555\u001b[39m     **kwargs,\n\u001b[32m    556\u001b[39m }\n\u001b[32m--> \u001b[39m\u001b[32m557\u001b[39m response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mclient\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcreate\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmessage_dicts\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    558\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._create_chat_result(response, params)\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\site-packages\\groq\\resources\\chat\\completions.py:368\u001b[39m, in \u001b[36mCompletions.create\u001b[39m\u001b[34m(self, messages, model, exclude_domains, frequency_penalty, function_call, functions, include_domains, logit_bias, logprobs, max_completion_tokens, max_tokens, metadata, n, parallel_tool_calls, presence_penalty, reasoning_effort, reasoning_format, response_format, search_settings, seed, service_tier, stop, store, stream, temperature, tool_choice, tools, top_logprobs, top_p, user, extra_headers, extra_query, extra_body, timeout)\u001b[39m\n\u001b[32m    181\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mcreate\u001b[39m(\n\u001b[32m    182\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m    183\u001b[39m     *,\n\u001b[32m   (...)\u001b[39m\u001b[32m    229\u001b[39m     timeout: \u001b[38;5;28mfloat\u001b[39m | httpx.Timeout | \u001b[38;5;28;01mNone\u001b[39;00m | NotGiven = NOT_GIVEN,\n\u001b[32m    230\u001b[39m ) -> ChatCompletion | Stream[ChatCompletionChunk]:\n\u001b[32m    231\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m    232\u001b[39m \u001b[33;03m    Creates a model response for the given chat conversation.\u001b[39;00m\n\u001b[32m    233\u001b[39m \n\u001b[32m   (...)\u001b[39m\u001b[32m    366\u001b[39m \u001b[33;03m      timeout: Override the client-level default timeout for this request, in seconds\u001b[39;00m\n\u001b[32m    367\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m368\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_post\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    369\u001b[39m \u001b[43m        \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m/openai/v1/chat/completions\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m    370\u001b[39m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmaybe_transform\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    371\u001b[39m \u001b[43m            \u001b[49m\u001b[43m{\u001b[49m\n\u001b[32m    372\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmessages\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    373\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmodel\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    374\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mexclude_domains\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mexclude_domains\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    375\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mfrequency_penalty\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfrequency_penalty\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    376\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mfunction_call\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunction_call\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    377\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mfunctions\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunctions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    378\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43minclude_domains\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43minclude_domains\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    379\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mlogit_bias\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mlogit_bias\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    380\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mlogprobs\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mlogprobs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    381\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmax_completion_tokens\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_completion_tokens\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    382\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmax_tokens\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_tokens\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    383\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmetadata\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmetadata\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    384\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mn\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    385\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mparallel_tool_calls\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mparallel_tool_calls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    386\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mpresence_penalty\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mpresence_penalty\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    387\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mreasoning_effort\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mreasoning_effort\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    388\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mreasoning_format\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mreasoning_format\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    389\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mresponse_format\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mresponse_format\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    390\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43msearch_settings\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43msearch_settings\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    391\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mseed\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mseed\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    392\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mservice_tier\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mservice_tier\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    393\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstop\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    394\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstore\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstore\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    395\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstream\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    396\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtemperature\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtemperature\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    397\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtool_choice\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtool_choice\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    398\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtools\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtools\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    399\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtop_logprobs\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtop_logprobs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    400\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtop_p\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtop_p\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    401\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43muser\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43muser\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    402\u001b[39m \u001b[43m            \u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    403\u001b[39m \u001b[43m            \u001b[49m\u001b[43mcompletion_create_params\u001b[49m\u001b[43m.\u001b[49m\u001b[43mCompletionCreateParams\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    404\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    405\u001b[39m \u001b[43m        \u001b[49m\u001b[43moptions\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmake_request_options\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    406\u001b[39m \u001b[43m            \u001b[49m\u001b[43mextra_headers\u001b[49m\u001b[43m=\u001b[49m\u001b[43mextra_headers\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mextra_query\u001b[49m\u001b[43m=\u001b[49m\u001b[43mextra_query\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mextra_body\u001b[49m\u001b[43m=\u001b[49m\u001b[43mextra_body\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\n\u001b[32m    407\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    408\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m=\u001b[49m\u001b[43mChatCompletion\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    409\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    410\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m=\u001b[49m\u001b[43mStream\u001b[49m\u001b[43m[\u001b[49m\u001b[43mChatCompletionChunk\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    411\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\site-packages\\groq\\_base_client.py:1232\u001b[39m, in \u001b[36mSyncAPIClient.post\u001b[39m\u001b[34m(self, path, cast_to, body, options, files, stream, stream_cls)\u001b[39m\n\u001b[32m   1218\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mpost\u001b[39m(\n\u001b[32m   1219\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   1220\u001b[39m     path: \u001b[38;5;28mstr\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1227\u001b[39m     stream_cls: \u001b[38;5;28mtype\u001b[39m[_StreamT] | \u001b[38;5;28;01mNone\u001b[39;00m = \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m   1228\u001b[39m ) -> ResponseT | _StreamT:\n\u001b[32m   1229\u001b[39m     opts = FinalRequestOptions.construct(\n\u001b[32m   1230\u001b[39m         method=\u001b[33m\"\u001b[39m\u001b[33mpost\u001b[39m\u001b[33m\"\u001b[39m, url=path, json_data=body, files=to_httpx_files(files), **options\n\u001b[32m   1231\u001b[39m     )\n\u001b[32m-> \u001b[39m\u001b[32m1232\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m cast(ResponseT, \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mopts\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m)\u001b[49m)\n", "\u001b[36mFile \u001b[39m\u001b[32me:\\GENAI\\genai\\Lib\\site-packages\\groq\\_base_client.py:1034\u001b[39m, in \u001b[36mSyncAPIClient.request\u001b[39m\u001b[34m(self, cast_to, options, stream, stream_cls)\u001b[39m\n\u001b[32m   1031\u001b[39m             err.response.read()\n\u001b[32m   1033\u001b[39m         log.debug(\u001b[33m\"\u001b[39m\u001b[33mRe-raising status error\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m-> \u001b[39m\u001b[32m1034\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m._make_status_error_from_response(err.response) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mN<PERSON>\u001b[39;00m\n\u001b[32m   1036\u001b[39m     \u001b[38;5;28;01mbreak\u001b[39;00m\n\u001b[32m   1038\u001b[39m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m response \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON>one\u001b[39;00m, \u001b[33m\"\u001b[39m\u001b[33mcould not resolve response (should never happen)\u001b[39m\u001b[33m\"\u001b[39m\n", "\u001b[31mBadRequestError\u001b[39m: Error code: 400 - {'error': {'message': \"Failed to call a function. Please adjust your prompt. See 'failed_generation' for more details.\", 'type': 'invalid_request_error', 'code': 'tool_use_failed', 'failed_generation': '{\"tool_use\":{\"tool_calls\":[{\"id\":\"pending\",\"type\":\"function\",\"function\":{\"name\":\"GradeHallucinations\"},\"parameters\":{\"binary_score\":\"yes\",\"explanation\":\"The answer is grounded in the facts provided, which are the latest advancements in machine learning. The LLM generation provides a clear and concise summary of the key developments in machine learning, and it is supported by the facts.\"}}]}}'}}", "During task with name 'grade_hallucination' and id 'dd4cf100-ad12-bfee-570f-93b4c619ea03'"]}], "source": ["# Initialize the RAG agent\n", "agent = RAGAgent()\n", "\n", "# Example question\n", "question = \"What are the latest developments in machine learning?\"\n", "\n", "# Run the workflow\n", "result = agent.run(question)\n", "\n", "print(\"\\n\" + \"=\"*50)\n", "print(\"FINAL RESULT:\")\n", "print(\"=\"*50)\n", "print(f\"Question: {result['question']}\")\n", "print(f\"Answer: {result['generation']}\")\n", "print(f\"Documents used: {len(result['documents'])}\")"]}, {"cell_type": "code", "execution_count": 38, "id": "c972aa4a", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<langgraph.graph.state.CompiledStateGraph object at 0x00000193961C2D90>"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["agent.workflow"]}, {"cell_type": "markdown", "id": "c7ab7f93", "metadata": {}, "source": ["## Agent exp -2"]}, {"cell_type": "code", "execution_count": 141, "id": "4aa99803", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4788\\**********.py:16: LangChainDeprecationWarning: As of langchain-core 0.3.0, LangChain uses pydantic v2 internally. The langchain.pydantic_v1 module was a compatibility shim for pydantic v1, and should no longer be used. Please update the code to import from Pydantic directly.\n", "\n", "For example, replace imports like: `from langchain.pydantic_v1 import BaseModel`\n", "with: `from pydantic import BaseModel`\n", "or the v1 compatibility namespace if you are working in a code base that has not been fully upgraded to pydantic 2 yet. \tfrom pydantic.v1 import BaseModel\n", "\n", "  from src.agent.nodes.grade import HallucinationGrader, RetrieverValidator\n"]}], "source": ["import os, sys\n", "from typing import Dict, Any, List\n", "from concurrent.futures import ThreadPoolExecutor\n", "from langchain_groq import ChatGroq\n", "from langchain.schema import Document\n", "from langgraph.graph import StateGraph, END\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "\n", "from src.exception import CustomException\n", "from src.agent.constant import GraphState\n", "from src.agent.nodes.web_search import TaivilySearchTool\n", "from src.agent.nodes.generation import GenerationTool\n", "from src.agent.nodes.vectoredb_retrieval import RetrieverTool\n", "from src.agent.nodes.grade import HallucinationGrader, RetrieverValidator\n", "from src.agent.route import QuestionRouter\n", "\n", "class AgenticRAG:\n", "    def __init__(self) -> None:\n", "        self.llm = self._load_model()\n", "\n", "        # Tools\n", "        self.web_search_tool = TaivilySearchTool()\n", "        self.rag_tool = RetrieverTool(self.llm)\n", "        self.generation_tool = GenerationTool(self.llm)\n", "        self.document_validator = RetrieverValidator(self.llm)\n", "        self.hallucination_grader = HallucinationGrader(self.llm)\n", "        self.question_router = QuestionRouter(self.llm)\n", "\n", "        self.workflow = self._build_graph()\n", "\n", "    def _load_model(self):\n", "        with ThreadPoolExecutor(max_workers=4) as executor:\n", "            return executor.submit(\n", "                <PERSON>t<PERSON><PERSON><PERSON>, model=\"llama3-8b-8192\", api_key=os.getenv(\"GROQ_API_KEY\")\n", "            ).result()\n", "\n", "    def _build_graph(self):\n", "        workflow = StateGraph(GraphState)\n", "\n", "        # Nodes\n", "        workflow.add_node(\"route_question\", self.question_router.route_question)\n", "        workflow.add_node(\"rag_tool\", self.rag_tool.search)\n", "        workflow.add_node(\"validate_documents\", self.document_validator.validate_documents)\n", "        workflow.add_node(\"web_search\", self.web_search_tool.web_search)\n", "        workflow.add_node(\"generate\", self.generation_tool.generate)\n", "        workflow.add_node(\"grade_hallucination\", self.hallucination_grader.grade_hallucination)\n", "\n", "        # Entry\n", "        workflow.set_entry_point(\"route_question\")\n", "        workflow.add_edge(\"route_question\", \"rag_tool\")\n", "        workflow.add_edge(\"rag_tool\", \"validate_documents\")\n", "\n", "        # After validation: if valid → generate, else → web_search\n", "        workflow.add_conditional_edges(\n", "            \"validate_documents\",\n", "            self._decide_after_validation,\n", "            {\n", "                \"generate\": \"generate\",\n", "                \"web_search\": \"web_search\"\n", "            }\n", "        )\n", "\n", "        # From web search → generate\n", "        workflow.add_edge(\"web_search\", \"generate\")\n", "\n", "        # After generate → hallucination grader\n", "        workflow.add_edge(\"generate\", \"grade_hallucination\")\n", "\n", "        # After hallucination grading: if hallucinated → web_search again, else → end\n", "        workflow.add_conditional_edges(\n", "            \"grade_hallucination\",\n", "            self._decide_after_hallucination_check,\n", "            {\n", "                \"web_search\": \"web_search\",\n", "                \"end\": END\n", "            }\n", "        )\n", "\n", "        return workflow.compile()\n", "\n", "    def _decide_after_validation(self, state: GraphState) -> str:\n", "        \"\"\"If validation fails, fallback to web search\"\"\"\n", "        if state.get(\"use_web_search\", False):\n", "            return \"web_search\"\n", "        return \"generate\"\n", "\n", "    def _decide_after_hallucination_check(self, state: GraphState) -> str:\n", "        \"\"\"If hallucination is True, go to web_search again\"\"\"\n", "        if state.get(\"use_web_search\", False):\n", "            return \"web_search\"\n", "        return \"end\"\n", "\n", "    def run(self, question: str, documents: List[Document] = None) -> Dict[str, Any]:\n", "        try:\n", "            initial_state = {\n", "                \"question\": question,\n", "                \"generation\": \"\",\n", "                \"use_web_search\": False,  # 👈 default assumes RAG is confident\n", "                \"documents\": documents or []\n", "            }\n", "            return self.workflow.invoke(initial_state)\n", "        except Exception as e:\n", "            raise CustomException(e, sys)\n"]}, {"cell_type": "code", "execution_count": 142, "id": "********", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading components...\n", "  Loading vector database...\n", "Components loaded!\n", "  Loading vector database...\n", "====================  Retriever Tool Invoked ====================\n", "==================== Question Routing ====================\n"]}, {"name": "stderr", "output_type": "stream", "text": ["--- Logging error ---\n", "Traceback (most recent call last):\n", "  File \"e:\\GENAI\\genai\\Lib\\logging\\__init__.py\", line 1110, in emit\n", "    msg = self.format(record)\n", "          ^^^^^^^^^^^^^^^^^^^\n", "  File \"e:\\GENAI\\genai\\Lib\\logging\\__init__.py\", line 953, in format\n", "    return fmt.format(record)\n", "           ^^^^^^^^^^^^^^^^^^\n", "  File \"e:\\GENAI\\genai\\Lib\\logging\\__init__.py\", line 687, in format\n", "    record.message = record.getMessage()\n", "                     ^^^^^^^^^^^^^^^^^^^\n", "  File \"e:\\GENAI\\genai\\Lib\\logging\\__init__.py\", line 377, in getMessage\n", "    msg = msg % self.args\n", "          ~~~~^~~~~~~~~~~\n", "TypeError: not all arguments converted during string formatting\n", "Call stack:\n", "  File \"<frozen runpy>\", line 198, in _run_module_as_main\n", "  File \"<frozen runpy>\", line 88, in _run_code\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\traitlets\\config\\application.py\", line 1075, in launch_instance\n", "    app.start()\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\ipykernel\\kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\tornado\\platform\\asyncio.py\", line 211, in start\n", "    self.asyncio_loop.run_forever()\n", "  File \"e:\\GENAI\\genai\\Lib\\asyncio\\base_events.py\", line 608, in run_forever\n", "    self._run_once()\n", "  File \"e:\\GENAI\\genai\\Lib\\asyncio\\base_events.py\", line 1936, in _run_once\n", "    handle._run()\n", "  File \"e:\\GENAI\\genai\\Lib\\asyncio\\events.py\", line 84, in _run\n", "    self._context.run(self._callback, *self._args)\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\ipykernel\\kernelbase.py\", line 516, in dispatch_queue\n", "    await self.process_one()\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\ipykernel\\kernelbase.py\", line 505, in process_one\n", "    await dispatch(*args)\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\ipykernel\\kernelbase.py\", line 397, in dispatch_shell\n", "    await result\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\ipykernel\\ipkernel.py\", line 368, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\ipykernel\\kernelbase.py\", line 752, in execute_request\n", "    reply_content = await reply_content\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\ipykernel\\ipkernel.py\", line 455, in do_execute\n", "    res = shell.run_cell(\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\ipykernel\\zmqshell.py\", line 577, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\IPython\\core\\interactiveshell.py\", line 3098, in run_cell\n", "    result = self._run_cell(\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\IPython\\core\\interactiveshell.py\", line 3153, in _run_cell\n", "    result = runner(coro)\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\IPython\\core\\async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\IPython\\core\\interactiveshell.py\", line 3365, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\IPython\\core\\interactiveshell.py\", line 3610, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\IPython\\core\\interactiveshell.py\", line 3670, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4788\\3297273500.py\", line 9, in <module>\n", "    result = agent.run(question)\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4788\\**********.py\", line 103, in run\n", "    return self.workflow.invoke(initial_state)\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\langgraph\\pregel\\__init__.py\", line 2719, in invoke\n", "    for chunk in self.stream(\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\langgraph\\pregel\\__init__.py\", line 2436, in stream\n", "    for _ in runner.tick(\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\langgraph\\pregel\\runner.py\", line 161, in tick\n", "    run_with_retry(\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\langgraph\\pregel\\retry.py\", line 40, in run_with_retry\n", "    return task.proc.invoke(task.input, config)\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\langgraph\\utils\\runnable.py\", line 623, in invoke\n", "    input = context.run(step.invoke, input, config, **kwargs)\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\langgraph\\utils\\runnable.py\", line 377, in invoke\n", "    ret = self.func(*args, **kwargs)\n", "  File \"d:\\genaiprojects\\calling-repasentive-ai\\calling-agent\\src\\agent\\route.py\", line 40, in route_question\n", "    logging.info(\"route: \",question)\n", "Message: 'route: '\n", "Arguments: ('What are the latest developments in machine learning?',)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Routing result: datasource='web_search'\n", "Routing to web search\n", " ================= routing completed =================\n", "What are the latest developments in machine learning?\n", "I don't know\n", "\n", "final result : I don't know \t web search : \t True\n", "====================  Retriever Tool Invoked Completed ====================\n", "==================== Document Validation Tool Invoked ====================\n", "---------- Not Relevant Document -----------\n", "==================== Document Validation Completed ====================\n", "==================== Web Search Tool Invoked ====================\n", "[{'url': 'https://ep.jhu.edu/news/advancements-in-ai-and-machine-learning/', 'title': 'Advancements in AI and Machine Learning', 'content': 'AI algorithms process vast datasets to provide insights that support strategic planning and resource allocation to streamline decision-making. This allows engineers to focus more on innovation and complex problem-solving.\\n\\n## Recent Advancements in AI and Machine Learning\\n\\nThe advancements in AI and ML have exponentially enhanced applications across multiple sectors. Here’s how AI and ML have changed various industries.\\n\\n### Breakthroughs in Generative AI and Large Language Models [...] ML is revolutionizing industries every day by making systems more intelligent and flexible. When they integrate ML, companies can transition from traditional robotic process automation to agentic AI, which combines deterministic software automation with non-deterministic AI capabilities like LLMs. This allows for improved enterprise workflows and positions companies to better orchestrate AI agents, human workers, and traditional automation across the board. [...] The field of generative AI has seen remarkable progress, particularly with the development of advanced Large Language Models (LLMs). Meta’s recent release of Llama 4 includes models like Scout and Maverick that are designed to handle politically and socially contentious questions more effectively than their predecessors. They demonstrate reduced political bias and can process diverse data types, including text, video, images, and audio. \\u200b', 'score': 0.58976424, 'raw_content': None}, {'url': 'https://www.nature.com/subjects/machine-learning', 'title': 'Machine learning - Latest research and news | Nature', 'content': '### Scalable 3D reconstruction for X-ray single particle imaging with online machine learning\\n\\nThe authors devise an algorithm to reconstruct proteins from X-ray single particle data, unlocking the ability to process datasets online, in a single pass. The technique matches or outperforms existing methods in simulation and on experimental data.\\n\\n### Integrated biotechnological and AI innovations for crop improvement [...] An artificial-intelligence tool helps historians to interpret Latin inscriptions by assigning a date and location and suggesting text for missing parts.\\n\\n### Artificial intelligence advances skull stripping across lifespan\\n\\nA skull-stripping artificial intelligence model leveraging personalized prior knowledge from brain atlases generates consistent brain extraction across diverse age groups, scanners and protocols.\\n\\n### Mapping cell fate transition in space and time [...] ### When cellular reprogramming meets AI: towards de novo cell design\\n\\nIn this Journal Club, Jian Shu recalls a 2006 publication by Takahashi and Yamanaka as well as a 2021 paper introducing AlphaFold to discuss the fascinating potential of cellular reprogramming in the age of artificial intelligence.\\n\\n### Mapping cell fate transition in space and time', 'score': 0.54942364, 'raw_content': None}, {'url': 'https://medium.com/@smith.emily2584/top-machine-learning-technology-trends-to-watch-in-2025-6f592879a746', 'title': 'Top Machine Learning Technology Trends to Watch in 2025 - Medium', 'content': 'Trend Insight:  \\n ML models are being deployed on edge devices — phones, sensors, drones, cameras — where bandwidth and latency are critical.\\n\\nWhy It Matters:\\n\\nUse Case Spotlight:  \\n Manufacturing plants are using edge ML for real-time quality inspection and predictive maintenance.\\n\\n# ⚡ 4. AutoML is Democratizing Machine Learning\\n\\nTrend Insight:  \\n AutoML tools are enabling non-experts to build models by automating feature selection, hyperparameter tuning, and model evaluation. [...] Sign up\\n\\nSign in\\n\\nSign up\\n\\nSign in\\n\\n# Top Machine Learning Technology Trends to Watch in 2025\\n\\nEmily Smith\\n\\n--\\n\\nListen\\n\\nShare\\n\\nMachine learning (ML) is evolving faster than ever. From scaling predictive models to powering real-time decisions, ML has gone from experimental to essential. In 2025, organizations are not just adopting machine learning, they’re transforming how it’s built, deployed, and governed.\\n\\nHere are the top ML technology trends shaping the landscape this year: [...] Tools to Watch:  \\n Mostly AI, Gretel.ai, NVIDIA Omniverse Replicator\\n\\n# ☁️ 8. Unified ML Platforms and MLOps\\n\\nTrend Insight:  \\nCompanies are moving away from fragmented ML pipelines to integrated platforms with built-in monitoring, retraining, and governance.\\n\\nWhy It Matters:\\n\\nKey Platforms:  \\n Azure Machine Learning, Vertex AI, AWS SageMaker, Databricks MLflow\\n\\n# 💬 9. Multimodal Machine Learning', 'score': 0.50856906, 'raw_content': None}]\n", "==================== Web Search Tool Completed ====================\n", "==================== Generation Tool Invoked ====================\n", "==================== Hallucination Grading ====================\n", "---DECISION: GE<PERSON>RATI<PERSON> IS GROUNDED IN DOCUMENTS---\n", "---CHECK ANSWER---\n", "---DECISION: ANSWER ADDRESSES THE USER QUESTION---\n", "\n", "==================================================\n", "FINAL RESULT:\n", "==================================================\n", "Question: What are the latest developments in machine learning?\n", "Answer: According to the provided context, the latest developments in machine learning (ML) include:\n", "\n", "1. **Generative AI and Large Language Models**: Breakthroughs in generative AI and large language models (LLMs) have exponentially enhanced applications across multiple sectors, enabling improved enterprise workflows and better orchestration of AI agents, human workers, and traditional automation.\n", "2. **AutoML**: AutoML tools are democratizing machine learning by automating feature selection, hyperparameter tuning, and model evaluation, enabling non-experts to build models.\n", "3. **Unified ML Platforms and MLOps**: Companies are moving away from fragmented ML pipelines to integrated platforms with built-in monitoring, retraining, and governance, such as Azure Machine Learning, Vertex AI, AWS SageMaker, and Databricks MLflow.\n", "4. **Multimodal Machine Learning**: Machine learning is being applied to various modalities, including text, video, images, and audio, enabling more comprehensive insights and decision-making.\n", "\n", "These developments are transforming the field of machine learning, making it more accessible, efficient, and effective for organizations to leverage AI and ML in their operations.\n", "object of type 'Document' has no len()\n"]}], "source": ["try:\n", "\t# Initialize the RAG agent\n", "\tagent = AgenticRAG()\n", "\n", "\t# Example question\n", "\tquestion = \"What are the latest developments in machine learning?\"\n", "\n", "\t# Run the workflow\n", "\tresult = agent.run(question)\n", "\n", "\tprint(\"\\n\" + \"=\"*50)\n", "\tprint(\"FINAL RESULT:\")\n", "\tprint(\"=\"*50)\n", "\tprint(f\"Question: {result['question']}\")\n", "\tprint(f\"Answer: {result['generation']}\")\n", "\tprint(f\"Documents used: {len(result['documents'])}\")\n", "except Exception as e:\n", "\tprint(e)"]}, {"cell_type": "code", "execution_count": 143, "id": "e8e6763f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================== Question Routing ====================\n"]}, {"name": "stderr", "output_type": "stream", "text": ["--- Logging error ---\n", "Traceback (most recent call last):\n", "  File \"e:\\GENAI\\genai\\Lib\\logging\\__init__.py\", line 1110, in emit\n", "    msg = self.format(record)\n", "          ^^^^^^^^^^^^^^^^^^^\n", "  File \"e:\\GENAI\\genai\\Lib\\logging\\__init__.py\", line 953, in format\n", "    return fmt.format(record)\n", "           ^^^^^^^^^^^^^^^^^^\n", "  File \"e:\\GENAI\\genai\\Lib\\logging\\__init__.py\", line 687, in format\n", "    record.message = record.getMessage()\n", "                     ^^^^^^^^^^^^^^^^^^^\n", "  File \"e:\\GENAI\\genai\\Lib\\logging\\__init__.py\", line 377, in getMessage\n", "    msg = msg % self.args\n", "          ~~~~^~~~~~~~~~~\n", "TypeError: not all arguments converted during string formatting\n", "Call stack:\n", "  File \"<frozen runpy>\", line 198, in _run_module_as_main\n", "  File \"<frozen runpy>\", line 88, in _run_code\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\traitlets\\config\\application.py\", line 1075, in launch_instance\n", "    app.start()\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\ipykernel\\kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\tornado\\platform\\asyncio.py\", line 211, in start\n", "    self.asyncio_loop.run_forever()\n", "  File \"e:\\GENAI\\genai\\Lib\\asyncio\\base_events.py\", line 608, in run_forever\n", "    self._run_once()\n", "  File \"e:\\GENAI\\genai\\Lib\\asyncio\\base_events.py\", line 1936, in _run_once\n", "    handle._run()\n", "  File \"e:\\GENAI\\genai\\Lib\\asyncio\\events.py\", line 84, in _run\n", "    self._context.run(self._callback, *self._args)\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\ipykernel\\kernelbase.py\", line 516, in dispatch_queue\n", "    await self.process_one()\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\ipykernel\\kernelbase.py\", line 505, in process_one\n", "    await dispatch(*args)\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\ipykernel\\kernelbase.py\", line 397, in dispatch_shell\n", "    await result\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\ipykernel\\ipkernel.py\", line 368, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\ipykernel\\kernelbase.py\", line 752, in execute_request\n", "    reply_content = await reply_content\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\ipykernel\\ipkernel.py\", line 455, in do_execute\n", "    res = shell.run_cell(\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\ipykernel\\zmqshell.py\", line 577, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\IPython\\core\\interactiveshell.py\", line 3098, in run_cell\n", "    result = self._run_cell(\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\IPython\\core\\interactiveshell.py\", line 3153, in _run_cell\n", "    result = runner(coro)\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\IPython\\core\\async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\IPython\\core\\interactiveshell.py\", line 3365, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\IPython\\core\\interactiveshell.py\", line 3610, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\IPython\\core\\interactiveshell.py\", line 3670, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4788\\2402053439.py\", line 7, in <module>\n", "    result = agent.run(question)\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4788\\**********.py\", line 103, in run\n", "    return self.workflow.invoke(initial_state)\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\langgraph\\pregel\\__init__.py\", line 2719, in invoke\n", "    for chunk in self.stream(\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\langgraph\\pregel\\__init__.py\", line 2436, in stream\n", "    for _ in runner.tick(\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\langgraph\\pregel\\runner.py\", line 161, in tick\n", "    run_with_retry(\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\langgraph\\pregel\\retry.py\", line 40, in run_with_retry\n", "    return task.proc.invoke(task.input, config)\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\langgraph\\utils\\runnable.py\", line 623, in invoke\n", "    input = context.run(step.invoke, input, config, **kwargs)\n", "  File \"e:\\GENAI\\genai\\Lib\\site-packages\\langgraph\\utils\\runnable.py\", line 377, in invoke\n", "    ret = self.func(*args, **kwargs)\n", "  File \"d:\\genaiprojects\\calling-repasentive-ai\\calling-agent\\src\\agent\\route.py\", line 40, in route_question\n", "    logging.info(\"route: \",question)\n", "Message: 'route: '\n", "Arguments: ('What are the specifications of ABB JX245 and SMPGB joint?',)\n"]}, {"name": "stdout", "output_type": "stream", "text": [" ================= routing completed =================\n", "Error occurred python script name [C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4788\\**********.py] line number [103] error message [Error occurred python script name [d:\\genaiprojects\\calling-repasentive-ai\\calling-agent\\src\\agent\\route.py] line number [42] error message [Error code: 400 - {'error': {'message': \"tool call validation failed: attempted to call tool 'WebSearch' which was not request.tools\", 'type': 'invalid_request_error', 'code': 'tool_use_failed', 'failed_generation': '<tool-use>\\n{\\n  \"tool_calls\": [\\n    {\\n      \"id\": \"pending\",\\n      \"type\": \"function\",\\n      \"function\": {\\n        \"name\": \"WebSearch\"\\n      },\\n      \"parameters\": {\\n        \"query\": \"What are the specifications of ABB JX245 and SMPGB joint?\"\\n      }\\n    }\\n  ]\\n}\\n</tool-use>'}}]]\n"]}], "source": ["try:\n", "\n", "\t# Example question\n", "\tquestion = \"What are the specifications of ABB JX245 and SMPGB joint?\"\n", "\n", "\t# Run the workflow\n", "\tresult = agent.run(question)\n", "\n", "\tprint(\"\\n\" + \"=\"*50)\n", "\tprint(\"FINAL RESULT:\")\n", "\tprint(\"=\"*50)\n", "\tprint(f\"Question: {result['question']}\")\n", "\tprint(f\"Answer: {result['generation']}\")\n", "\tprint(f\"Documents used: {len(result['documents'])}\")\n", "except Exception as e:\n", "\tprint(e)"]}, {"cell_type": "code", "execution_count": 144, "id": "7167a75c", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<langgraph.graph.state.CompiledStateGraph object at 0x00000193C1332450>"]}, "execution_count": 144, "metadata": {}, "output_type": "execute_result"}], "source": ["agent.workflow"]}, {"cell_type": "code", "execution_count": 136, "id": "4dc31b2d", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'CompiledStateGraph' object has no attribute 'workflow'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36m<PERSON>ell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[136]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[43mgraph\u001b[49m\u001b[43m.\u001b[49m\u001b[43mworkflow\u001b[49m\n", "\u001b[31mAttributeError\u001b[39m: 'CompiledStateGraph' object has no attribute 'workflow'"]}], "source": ["graph.workflow"]}, {"cell_type": "code", "execution_count": null, "id": "be96ccf9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}