# FastAPI + LiveKit Agent Integration

## Overview

This document describes the integration of FastAPI endpoints directly into the `langgraph_agent.py` file, allowing both the LiveKit agent and the FastAPI server to run from a single process.

## Changes Made

### 1. Integrated FastAPI into `langgraph_agent.py`

- **Added FastAPI imports and setup** at the top of the file
- **Moved all API endpoints** from `app.py` into `langgraph_agent.py`
- **Added threading support** to run both services simultaneously
- **Maintained all existing functionality** for both the agent and API

### 2. Key Features Integrated

#### FastAPI Endpoints:
- `/api/health` - Health check endpoint
- `/api/save-settings` - Save model configuration
- `/api/load-settings` - Load model configuration  
- `/api/available-models` - Get available STT/LLM/TTS models
- `/api/call-logs` - Get call logs
- `/api/call-logs/{filename}` - Get specific call log details
- `/api/transcriptions` - Get recent transcriptions
- `/api/live-transcription` - Get live transcription data
- `/api/active-calls` - Get currently active calls
- `/api/audio-levels` - Get real-time audio levels
- `/api/export-logs` - Export all logs
- `/api/clear-logs` - Clear all call logs
- `/{path:path}` - Serve React app (catch-all route)

#### CORS Configuration:
- Supports multiple frontend origins
- Allows all necessary HTTP methods
- Handles preflight OPTIONS requests

#### Middleware:
- Request logging middleware
- CORS middleware for cross-origin requests

### 3. Execution Flow

```python
if __name__ == "__main__":
    # 1. Start FastAPI server in a separate thread
    fastapi_thread = threading.Thread(target=run_fastapi_server, daemon=True)
    fastapi_thread.start()
    
    # 2. Start LiveKit agent in the main thread
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint, agent_name="telephony_agent"))
```

## Benefits

1. **Single Process Deployment**: No need to manage two separate processes
2. **Simplified Configuration**: One configuration file for both services
3. **Shared Resources**: Both services can access the same global variables and instances
4. **Easier Monitoring**: Single process to monitor and manage
5. **Reduced Resource Usage**: Lower memory and CPU overhead

## Usage

### Starting the Integrated Service

```bash
python langgraph_agent.py
```

This will start:
- **FastAPI server** on `http://localhost:8000` (in background thread)
- **LiveKit agent** for voice conversations (main thread)

### Testing the Integration

Run the test script to verify both services are working:

```bash
python test_integration.py
```

### Accessing the Services

- **Frontend/API**: `http://localhost:8000`
- **API Documentation**: `http://localhost:8000/docs` (FastAPI auto-generated)
- **Health Check**: `http://localhost:8000/api/health`

## Configuration

The integrated service uses the same configuration as before:
- **Config file**: `config/model_settings.json`
- **Environment variables**: `.env` file
- **Log directory**: `call_logs/`

## Migration from Separate Services

If you were previously running `app.py` and `langgraph_agent.py` separately:

1. **Stop both services**
2. **Start the integrated service**: `python langgraph_agent.py`
3. **Verify functionality** using the test script
4. **Update deployment scripts** to use single process

## Troubleshooting

### Port Conflicts
If port 8000 is already in use, modify the `run_fastapi_server()` function:
```python
uvicorn.run(app, host="0.0.0.0", port=8001, ...)  # Change port
```

### Thread Issues
If you experience threading issues, ensure:
- No blocking operations in the FastAPI thread
- Proper exception handling in both threads
- Use `daemon=True` for the FastAPI thread

### Memory Usage
Monitor memory usage as both services now run in the same process:
- Use process monitoring tools
- Consider resource limits if needed

## Files Modified

- ✅ `langgraph_agent.py` - Integrated FastAPI endpoints and threading
- 📄 `app.py` - Can now be removed or kept as backup
- 📄 `test_integration.py` - Test script for verification

## Next Steps

1. Test the integrated service thoroughly
2. Update deployment configurations
3. Monitor performance and resource usage
4. Consider removing the old `app.py` file once integration is confirmed working
